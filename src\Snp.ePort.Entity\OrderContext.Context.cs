﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Snp.ePort.Entity
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class OrderEntities : DbContext
    {
        public OrderEntities()
            : base("name=OrderEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<AM_ACTION> AM_ACTION { get; set; }
        public virtual DbSet<AM_APPLOG> AM_APPLOG { get; set; }
        public virtual DbSet<AM_FEATURE> AM_FEATURE { get; set; }
        public virtual DbSet<AM_GROUP_MENU> AM_GROUP_MENU { get; set; }
        public virtual DbSet<AM_GROUP_OPER_METHOD> AM_GROUP_OPER_METHOD { get; set; }
        public virtual DbSet<AM_MENU> AM_MENU { get; set; }
        public virtual DbSet<AM_OPER_TYPE> AM_OPER_TYPE { get; set; }
        public virtual DbSet<AM_PARTNER> AM_PARTNER { get; set; }
        public virtual DbSet<AM_ROLE> AM_ROLE { get; set; }
        public virtual DbSet<AM_ROLE_FEATURE> AM_ROLE_FEATURE { get; set; }
        public virtual DbSet<AM_SITE> AM_SITE { get; set; }
        public virtual DbSet<AM_SITE_GROUP_OPER_METHOD> AM_SITE_GROUP_OPER_METHOD { get; set; }
        public virtual DbSet<AM_TRANSPORT_TYPE> AM_TRANSPORT_TYPE { get; set; }
        public virtual DbSet<AM_USER_SITE_ROLE> AM_USER_SITE_ROLE { get; set; }
        public virtual DbSet<CFG_SITE_PARAMS> CFG_SITE_PARAMS { get; set; }
        public virtual DbSet<CFG_SITE_SERVCE_URL> CFG_SITE_SERVCE_URL { get; set; }
        public virtual DbSet<CHARGE> CHARGE { get; set; }
        public virtual DbSet<ERROR_DESC> ERROR_DESC { get; set; }
        public virtual DbSet<EXTRA_PHONE_NUMBER> EXTRA_PHONE_NUMBER { get; set; }
        public virtual DbSet<GP_SEQUENCE_GEN> GP_SEQUENCE_GEN { get; set; }
        public virtual DbSet<INVOICE_DETAILS> INVOICE_DETAILS { get; set; }
        public virtual DbSet<ORDER> ORDER { get; set; }
        public virtual DbSet<sysdiagrams> sysdiagrams { get; set; }
        public virtual DbSet<TRANSACTION> TRANSACTION { get; set; }
        public virtual DbSet<TRANSPORT> TRANSPORT { get; set; }
        public virtual DbSet<TRANSPORT_DETAIL> TRANSPORT_DETAIL { get; set; }
        public virtual DbSet<USER_ACTIVATION> USER_ACTIVATION { get; set; }
        public virtual DbSet<CFG_SITE_MODULE> CFG_SITE_MODULE { get; set; }
        public virtual DbSet<AM_OPER_METHOD> AM_OPER_METHOD { get; set; }
        public virtual DbSet<ORDER_DETAIL_OOG> ORDER_DETAIL_OOG { get; set; }
        public virtual DbSet<CONTAINER_VGM> CONTAINER_VGM { get; set; }
        public virtual DbSet<ORDER_DETAIL_PROPERTY> ORDER_DETAIL_PROPERTY { get; set; }
        public virtual DbSet<AM_ACCOUNT_TYPE> AM_ACCOUNT_TYPE { get; set; }
        public virtual DbSet<VE_ICD_CONT_INFO> VE_ICD_CONT_INFO { get; set; }
        public virtual DbSet<AM_CATEGORY> AM_CATEGORY { get; set; }
        public virtual DbSet<AM_DELIVERY_RECEIVE> AM_DELIVERY_RECEIVE { get; set; }
        public virtual DbSet<AM_FULL_EMPTY> AM_FULL_EMPTY { get; set; }
        public virtual DbSet<AM_OPER_METHOD_SETTING> AM_OPER_METHOD_SETTING { get; set; }
        public virtual DbSet<AM_DISCOUNT_UNIT> AM_DISCOUNT_UNIT { get; set; }
        public virtual DbSet<VE_OPER_METHOD_SETTING> VE_OPER_METHOD_SETTING { get; set; }
        public virtual DbSet<AM_DISCOUNT_SETTING> AM_DISCOUNT_SETTING { get; set; }
        public virtual DbSet<AM_SITE_SETTING> AM_SITE_SETTING { get; set; }
        public virtual DbSet<AM_ACTIVITY_LOG> AM_ACTIVITY_LOG { get; set; }
        public virtual DbSet<AM_SPECIAL_HDL_CODE> AM_SPECIAL_HDL_CODE { get; set; }
        public virtual DbSet<CUSTOMS_CARGO_ID> CUSTOMS_CARGO_ID { get; set; }
        public virtual DbSet<CUSTOMS_DECLARATION> CUSTOMS_DECLARATION { get; set; }
        public virtual DbSet<AM_CATEGORY_GROUP> AM_CATEGORY_GROUP { get; set; }
        public virtual DbSet<CUSTOMS_EXCEPTION_CONTAINER> CUSTOMS_EXCEPTION_CONTAINER { get; set; }
        public virtual DbSet<AM_USER> AM_USER { get; set; }
        public virtual DbSet<ORDER_DETAIL_TRUCK> ORDER_DETAIL_TRUCK { get; set; }
        public virtual DbSet<TRUCK> TRUCK { get; set; }
        public virtual DbSet<TRUCK_TRACKING> TRUCK_TRACKING { get; set; }
        public virtual DbSet<AM_INVOICE_STATUS> AM_INVOICE_STATUS { get; set; }
        public virtual DbSet<AM_SYSTEM_CODE> AM_SYSTEM_CODE { get; set; }
        public virtual DbSet<CFG_OPER_METHOD_DECL_TRANSP_TRK> CFG_OPER_METHOD_DECL_TRANSP_TRK { get; set; }
        public virtual DbSet<AM_PAYMENT_METHOD> AM_PAYMENT_METHOD { get; set; }
        public virtual DbSet<AM_SYSTEM_CATEGORY> AM_SYSTEM_CATEGORY { get; set; }
        public virtual DbSet<CFG_SYS_CATEGORY_OPR_METHOD> CFG_SYS_CATEGORY_OPR_METHOD { get; set; }
        public virtual DbSet<ORDER_DETAIL_OTP> ORDER_DETAIL_OTP { get; set; }
        public virtual DbSet<ORDER_DETAIL_IMO> ORDER_DETAIL_IMO { get; set; }
        public virtual DbSet<SERVICE_CONFIG> SERVICE_CONFIG { get; set; }
        public virtual DbSet<AM_EMAIL_TEMPLATE> AM_EMAIL_TEMPLATE { get; set; }
        public virtual DbSet<VIEW_ORDER_DETAIL_PORT_CHANGE_INFORMATION> VIEW_ORDER_DETAIL_PORT_CHANGE_INFORMATION { get; set; }
        public virtual DbSet<AM_NOTIFICATION> AM_NOTIFICATION { get; set; }
        public virtual DbSet<AM_EMAIL_SMS_CONFIG> AM_EMAIL_SMS_CONFIG { get; set; }
        public virtual DbSet<AM_SERVICE> AM_SERVICE { get; set; }
        public virtual DbSet<AM_SITE_INFO> AM_SITE_INFO { get; set; }
        public virtual DbSet<AM_CONT_STATUS> AM_CONT_STATUS { get; set; }
        public virtual DbSet<INVOICE> INVOICE { get; set; }
        public virtual DbSet<ORDER_DETAIL_DOCUMENT> ORDER_DETAIL_DOCUMENT { get; set; }
        public virtual DbSet<AM_AUTO_APPROVAL> AM_AUTO_APPROVAL { get; set; }
        public virtual DbSet<AM_CHE_TYPE> AM_CHE_TYPE { get; set; }
        public virtual DbSet<CURRENCY_UNIT> CURRENCY_UNIT { get; set; }
        public virtual DbSet<EXCHANGE_RATE> EXCHANGE_RATE { get; set; }
        public virtual DbSet<AM_USER_BACKUP> AM_USER_BACKUP { get; set; }
        public virtual DbSet<ORDER_DETAIL_TEMP> ORDER_DETAIL_TEMP { get; set; }
        public virtual DbSet<AM_OPER_METHOD_CONFIG> AM_OPER_METHOD_CONFIG { get; set; }
        public virtual DbSet<AM_EPORT_SETTING> AM_EPORT_SETTING { get; set; }
        public virtual DbSet<ACCOMPANIED_SERVICE> ACCOMPANIED_SERVICE { get; set; }
        public virtual DbSet<CFG_DECLARE_TRANSPORT> CFG_DECLARE_TRANSPORT { get; set; }
        public virtual DbSet<ORDER_TRUCK> ORDER_TRUCK { get; set; }
        public virtual DbSet<VIEW_VES_VOYAGES_LLPOD_CHANGE_DETAIL> VIEW_VES_VOYAGES_LLPOD_CHANGE_DETAIL { get; set; }
        public virtual DbSet<ORDER_DETAIL_SERVICE> ORDER_DETAIL_SERVICE { get; set; }
        public virtual DbSet<AM_ROLE_OPER_METHOD> AM_ROLE_OPER_METHOD { get; set; }
        public virtual DbSet<AM_AGENT_CONFIG> AM_AGENT_CONFIG { get; set; }
        public virtual DbSet<CFG_TIME_PAYMENT> CFG_TIME_PAYMENT { get; set; }
        public virtual DbSet<ORDER_DETAIL_LOGISTIC> ORDER_DETAIL_LOGISTIC { get; set; }
        public virtual DbSet<ORDER_LOGISTIC> ORDER_LOGISTIC { get; set; }
        public virtual DbSet<AM_USER_LINER> AM_USER_LINER { get; set; }
        public virtual DbSet<AM_OPER_METHOD_SP_ARISE> AM_OPER_METHOD_SP_ARISE { get; set; }
        public virtual DbSet<CFG_SPECIAL_CODE> CFG_SPECIAL_CODE { get; set; }
        public virtual DbSet<ORDER_DETAIL_CONT_MOVEMENT> ORDER_DETAIL_CONT_MOVEMENT { get; set; }
        public virtual DbSet<AM_MENU_OPER_TYPE> AM_MENU_OPER_TYPE { get; set; }
        public virtual DbSet<VESSEL_VOYAGE_PORT_CHANGE_SERVICES> VESSEL_VOYAGE_PORT_CHANGE_SERVICES { get; set; }
        public virtual DbSet<ORDER_DETAIL_PORT_CHANGE> ORDER_DETAIL_PORT_CHANGE { get; set; }
        public virtual DbSet<AM_EPORT_CODE> AM_EPORT_CODE { get; set; }
        public virtual DbSet<AM_SMS_TEMPLATE> AM_SMS_TEMPLATE { get; set; }
        public virtual DbSet<CHECK_IN_DETAIL> CHECK_IN_DETAIL { get; set; }
        public virtual DbSet<CHECK_IN_ITEM> CHECK_IN_ITEM { get; set; }
        public virtual DbSet<AM_USER_TOKEN> AM_USER_TOKEN { get; set; }
        public virtual DbSet<AM_USER_AGENT> AM_USER_AGENT { get; set; }
        public virtual DbSet<ORDER_DETAIL_EBOOKING> ORDER_DETAIL_EBOOKING { get; set; }
        public virtual DbSet<VE_ORDER_DETAIL> VE_ORDER_DETAIL { get; set; }
        public virtual DbSet<TRANSACTION_DEBIT_COMPLETED> TRANSACTION_DEBIT_COMPLETED { get; set; }
        public virtual DbSet<AM_INVOICE_PATTERN> AM_INVOICE_PATTERN { get; set; }
        public virtual DbSet<AM_INVOICE_SERIAL> AM_INVOICE_SERIAL { get; set; }
        public virtual DbSet<DISTRICT> DISTRICTs { get; set; }
        public virtual DbSet<PROVINCE> PROVINCEs { get; set; }
        public virtual DbSet<WARD> WARDs { get; set; }
        public virtual DbSet<CHECK_IN_SHIP_INFO> CHECK_IN_SHIP_INFO { get; set; }
        public virtual DbSet<AM_CONFIG_CATEGORY> AM_CONFIG_CATEGORY { get; set; }
        public virtual DbSet<AM_CONFIG_SETTING> AM_CONFIG_SETTING { get; set; }
        public virtual DbSet<CFG_CUST_DECL> CFG_CUST_DECL { get; set; }
        public virtual DbSet<ORDER_DETAIL_CHECK_IN> ORDER_DETAIL_CHECK_IN { get; set; }
        public virtual DbSet<ORDER_DETAIL> ORDER_DETAIL { get; set; }
        public virtual DbSet<ADMIN_HELP_DESK_LOGS> ADMIN_HELP_DESK_LOGS { get; set; }
        public virtual DbSet<ORDER_DETAIL_COMMODITY> ORDER_DETAIL_COMMODITY { get; set; }
        public virtual DbSet<GROUP_CONNECTION> GROUP_CONNECTION { get; set; }
        public virtual DbSet<GROUP_MEMBER> GROUP_MEMBER { get; set; }
        public virtual DbSet<USER_CONNECTION> USER_CONNECTION { get; set; }
        public virtual DbSet<AM_VEHICLE_PERSONNELS> AM_VEHICLE_PERSONNELS { get; set; }
        public virtual DbSet<MSDS_FILE> MSDS_FILE { get; set; }
        public virtual DbSet<ORDER_DETAIL_MSDS_MAP> ORDER_DETAIL_MSDS_MAP { get; set; }
    
        public virtual ObjectResult<AM_OPER_METHOD> GET_OPER_METHOD(string uSER_NAME, string oPER_TYPE)
        {
            var uSER_NAMEParameter = uSER_NAME != null ?
                new ObjectParameter("USER_NAME", uSER_NAME) :
                new ObjectParameter("USER_NAME", typeof(string));
    
            var oPER_TYPEParameter = oPER_TYPE != null ?
                new ObjectParameter("OPER_TYPE", oPER_TYPE) :
                new ObjectParameter("OPER_TYPE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<AM_OPER_METHOD>("GET_OPER_METHOD", uSER_NAMEParameter, oPER_TYPEParameter);
        }
    
        public virtual ObjectResult<AM_OPER_METHOD> GET_OPER_METHOD(string uSER_NAME, string oPER_TYPE, MergeOption mergeOption)
        {
            var uSER_NAMEParameter = uSER_NAME != null ?
                new ObjectParameter("USER_NAME", uSER_NAME) :
                new ObjectParameter("USER_NAME", typeof(string));
    
            var oPER_TYPEParameter = oPER_TYPE != null ?
                new ObjectParameter("OPER_TYPE", oPER_TYPE) :
                new ObjectParameter("OPER_TYPE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<AM_OPER_METHOD>("GET_OPER_METHOD", mergeOption, uSER_NAMEParameter, oPER_TYPEParameter);
        }
    
        public virtual ObjectResult<GenerateNextNumber_Result> GenerateNextNumber(string trans_Type, Nullable<System.DateTime> currentDateInput)
        {
            var trans_TypeParameter = trans_Type != null ?
                new ObjectParameter("Trans_Type", trans_Type) :
                new ObjectParameter("Trans_Type", typeof(string));
    
            var currentDateInputParameter = currentDateInput.HasValue ?
                new ObjectParameter("CurrentDateInput", currentDateInput) :
                new ObjectParameter("CurrentDateInput", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GenerateNextNumber_Result>("GenerateNextNumber", trans_TypeParameter, currentDateInputParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_REPORT_Result> GET_TRANSACTION_REPORT(string sITE_ID, string uSER_NAME, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string iNVOICE_STATUS, string gATE_IN, Nullable<int> oRDER_ID, string oRDER_DETAIL_NO, string iNVOICE_NO, string tAX_FILE_NO, string sO_PHIEU, string oPER_METHOD, string pAID_BY_USER, string cREATEDBY, Nullable<int> hASEDO)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var uSER_NAMEParameter = uSER_NAME != null ?
                new ObjectParameter("USER_NAME", uSER_NAME) :
                new ObjectParameter("USER_NAME", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var iNVOICE_STATUSParameter = iNVOICE_STATUS != null ?
                new ObjectParameter("INVOICE_STATUS", iNVOICE_STATUS) :
                new ObjectParameter("INVOICE_STATUS", typeof(string));
    
            var gATE_INParameter = gATE_IN != null ?
                new ObjectParameter("GATE_IN", gATE_IN) :
                new ObjectParameter("GATE_IN", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var oRDER_DETAIL_NOParameter = oRDER_DETAIL_NO != null ?
                new ObjectParameter("ORDER_DETAIL_NO", oRDER_DETAIL_NO) :
                new ObjectParameter("ORDER_DETAIL_NO", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var sO_PHIEUParameter = sO_PHIEU != null ?
                new ObjectParameter("SO_PHIEU", sO_PHIEU) :
                new ObjectParameter("SO_PHIEU", typeof(string));
    
            var oPER_METHODParameter = oPER_METHOD != null ?
                new ObjectParameter("OPER_METHOD", oPER_METHOD) :
                new ObjectParameter("OPER_METHOD", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var cREATEDBYParameter = cREATEDBY != null ?
                new ObjectParameter("CREATEDBY", cREATEDBY) :
                new ObjectParameter("CREATEDBY", typeof(string));
    
            var hASEDOParameter = hASEDO.HasValue ?
                new ObjectParameter("HASEDO", hASEDO) :
                new ObjectParameter("HASEDO", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_REPORT_Result>("GET_TRANSACTION_REPORT", sITE_IDParameter, uSER_NAMEParameter, fROM_DATEParameter, tO_DATEParameter, iNVOICE_STATUSParameter, gATE_INParameter, oRDER_IDParameter, oRDER_DETAIL_NOParameter, iNVOICE_NOParameter, tAX_FILE_NOParameter, sO_PHIEUParameter, oPER_METHODParameter, pAID_BY_USERParameter, cREATEDBYParameter, hASEDOParameter);
        }
    
        public virtual ObjectResult<PR_GET_INVOICES_BY_ORDER_Result> GET_INVOICES_BY_ORDER(string siteId, Nullable<int> oRDERID)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var oRDERIDParameter = oRDERID.HasValue ?
                new ObjectParameter("ORDERID", oRDERID) :
                new ObjectParameter("ORDERID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_INVOICES_BY_ORDER_Result>("GET_INVOICES_BY_ORDER", siteIdParameter, oRDERIDParameter);
        }
    
        public virtual ObjectResult<PR_GET_EPORT_INVOICE_REPORT_Result> GET_EPORT_INVOICE_REPORT(string siteID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_EPORT_INVOICE_REPORT_Result>("GET_EPORT_INVOICE_REPORT", siteIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_REGISTER_DEBIT_LIST_Result> GET_REGISTER_DEBIT_LIST(string siteID, string fORWARDERTAXCODE, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fORWARDERTAXCODEParameter = fORWARDERTAXCODE != null ?
                new ObjectParameter("FORWARDERTAXCODE", fORWARDERTAXCODE) :
                new ObjectParameter("FORWARDERTAXCODE", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REGISTER_DEBIT_LIST_Result>("GET_REGISTER_DEBIT_LIST", siteIDParameter, fORWARDERTAXCODEParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_CHARGE_INFORMATION_Result> GET_CHARGE_INFORMATION(string transCode, string siteId)
        {
            var transCodeParameter = transCode != null ?
                new ObjectParameter("TransCode", transCode) :
                new ObjectParameter("TransCode", typeof(string));
    
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_CHARGE_INFORMATION_Result>("GET_CHARGE_INFORMATION", transCodeParameter, siteIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_EPORT_PAYMENT_REPORT_Result> GET_EPORT_PAYMENT_REPORT(string siteID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_EPORT_PAYMENT_REPORT_Result>("GET_EPORT_PAYMENT_REPORT", siteIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<AM_SITE> GET_CONFIG_SITE(string pModule)
        {
            var pModuleParameter = pModule != null ?
                new ObjectParameter("pModule", pModule) :
                new ObjectParameter("pModule", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<AM_SITE>("GET_CONFIG_SITE", pModuleParameter);
        }
    
        public virtual ObjectResult<AM_SITE> GET_CONFIG_SITE(string pModule, MergeOption mergeOption)
        {
            var pModuleParameter = pModule != null ?
                new ObjectParameter("pModule", pModule) :
                new ObjectParameter("pModule", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<AM_SITE>("GET_CONFIG_SITE", mergeOption, pModuleParameter);
        }
    
        public virtual ObjectResult<CFG_SITE_SERVCE_URL> GET_SERVICE_URL(string pSite, string pService)
        {
            var pSiteParameter = pSite != null ?
                new ObjectParameter("pSite", pSite) :
                new ObjectParameter("pSite", typeof(string));
    
            var pServiceParameter = pService != null ?
                new ObjectParameter("pService", pService) :
                new ObjectParameter("pService", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CFG_SITE_SERVCE_URL>("GET_SERVICE_URL", pSiteParameter, pServiceParameter);
        }
    
        public virtual ObjectResult<CFG_SITE_SERVCE_URL> GET_SERVICE_URL(string pSite, string pService, MergeOption mergeOption)
        {
            var pSiteParameter = pSite != null ?
                new ObjectParameter("pSite", pSite) :
                new ObjectParameter("pSite", typeof(string));
    
            var pServiceParameter = pService != null ?
                new ObjectParameter("pService", pService) :
                new ObjectParameter("pService", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<CFG_SITE_SERVCE_URL>("GET_SERVICE_URL", mergeOption, pSiteParameter, pServiceParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSFER_PAYMENT_REPORT_Result> GET_TRANSFER_PAYMENT_REPORT(string siteID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSFER_PAYMENT_REPORT_Result>("GET_TRANSFER_PAYMENT_REPORT", siteIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<ORDER> GET_ORDERS_NOT_INVOICE(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<int> userId)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ORDER>("GET_ORDERS_NOT_INVOICE", fromDateParameter, toDateParameter, userIdParameter);
        }
    
        public virtual ObjectResult<ORDER> GET_ORDERS_NOT_INVOICE(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<int> userId, MergeOption mergeOption)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ORDER>("GET_ORDERS_NOT_INVOICE", mergeOption, fromDateParameter, toDateParameter, userIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_REFUND_Result> GET_TRANSACTION_REFUND(string sITE_ID, string uSERNAME, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE, string tRANSACTION_ID)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var uSERNAMEParameter = uSERNAME != null ?
                new ObjectParameter("USERNAME", uSERNAME) :
                new ObjectParameter("USERNAME", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            var tRANSACTION_IDParameter = tRANSACTION_ID != null ?
                new ObjectParameter("TRANSACTION_ID", tRANSACTION_ID) :
                new ObjectParameter("TRANSACTION_ID", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_REFUND_Result>("GET_TRANSACTION_REFUND", sITE_IDParameter, uSERNAMEParameter, fROMDATEParameter, tODATEParameter, tRANSACTION_IDParameter);
        }
    
        public virtual ObjectResult<VE_ICD_CONT_INFO> GET_CONTS_OF_ICD(string siteID, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<int> delFlag)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var delFlagParameter = delFlag.HasValue ?
                new ObjectParameter("delFlag", delFlag) :
                new ObjectParameter("delFlag", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VE_ICD_CONT_INFO>("GET_CONTS_OF_ICD", siteIDParameter, fromDateParameter, toDateParameter, delFlagParameter);
        }
    
        public virtual ObjectResult<VE_ICD_CONT_INFO> GET_CONTS_OF_ICD(string siteID, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<int> delFlag, MergeOption mergeOption)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var delFlagParameter = delFlag.HasValue ?
                new ObjectParameter("delFlag", delFlag) :
                new ObjectParameter("delFlag", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VE_ICD_CONT_INFO>("GET_CONTS_OF_ICD", mergeOption, siteIDParameter, fromDateParameter, toDateParameter, delFlagParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_BY_ORDER_Result> GET_TRANSACTION_BY_ORDER(string siteId, Nullable<int> oRDERID)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var oRDERIDParameter = oRDERID.HasValue ?
                new ObjectParameter("ORDERID", oRDERID) :
                new ObjectParameter("ORDERID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_BY_ORDER_Result>("GET_TRANSACTION_BY_ORDER", siteIdParameter, oRDERIDParameter);
        }
    
        public virtual ObjectResult<PR_GET_USER_FEATURE_LIST_Result> PR_GET_USER_FEATURE_LIST(string siteId, Nullable<int> userId, Nullable<int> featureId)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(int));
    
            var featureIdParameter = featureId.HasValue ?
                new ObjectParameter("featureId", featureId) :
                new ObjectParameter("featureId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_USER_FEATURE_LIST_Result>("PR_GET_USER_FEATURE_LIST", siteIdParameter, userIdParameter, featureIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_EPORT_BARGE_LIST_Result> PR_GET_EPORT_BARGE_LIST(string vESID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE, string sITEID)
        {
            var vESIDParameter = vESID != null ?
                new ObjectParameter("VESID", vESID) :
                new ObjectParameter("VESID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_EPORT_BARGE_LIST_Result>("PR_GET_EPORT_BARGE_LIST", vESIDParameter, fROMDATEParameter, tODATEParameter, sITEIDParameter);
        }
    
        public virtual int PR_UPDATE_TRANSACTION(string transactionId, string paymentRefNo)
        {
            var transactionIdParameter = transactionId != null ?
                new ObjectParameter("TransactionId", transactionId) :
                new ObjectParameter("TransactionId", typeof(string));
    
            var paymentRefNoParameter = paymentRefNo != null ?
                new ObjectParameter("PaymentRefNo", paymentRefNo) :
                new ObjectParameter("PaymentRefNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("PR_UPDATE_TRANSACTION", transactionIdParameter, paymentRefNoParameter);
        }
    
        public virtual ObjectResult<VE_OPER_METHOD_SETTING> GET_OPER_METHOD_CONFIG(string siteId, Nullable<int> drId, Nullable<int> feId, Nullable<int> transTypeId, Nullable<int> catId, Nullable<int> roleId, Nullable<int> groupOperMethodId, Nullable<int> featureId)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var drIdParameter = drId.HasValue ?
                new ObjectParameter("drId", drId) :
                new ObjectParameter("drId", typeof(int));
    
            var feIdParameter = feId.HasValue ?
                new ObjectParameter("feId", feId) :
                new ObjectParameter("feId", typeof(int));
    
            var transTypeIdParameter = transTypeId.HasValue ?
                new ObjectParameter("transTypeId", transTypeId) :
                new ObjectParameter("transTypeId", typeof(int));
    
            var catIdParameter = catId.HasValue ?
                new ObjectParameter("catId", catId) :
                new ObjectParameter("catId", typeof(int));
    
            var roleIdParameter = roleId.HasValue ?
                new ObjectParameter("roleId", roleId) :
                new ObjectParameter("roleId", typeof(int));
    
            var groupOperMethodIdParameter = groupOperMethodId.HasValue ?
                new ObjectParameter("groupOperMethodId", groupOperMethodId) :
                new ObjectParameter("groupOperMethodId", typeof(int));
    
            var featureIdParameter = featureId.HasValue ?
                new ObjectParameter("featureId", featureId) :
                new ObjectParameter("featureId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VE_OPER_METHOD_SETTING>("GET_OPER_METHOD_CONFIG", siteIdParameter, drIdParameter, feIdParameter, transTypeIdParameter, catIdParameter, roleIdParameter, groupOperMethodIdParameter, featureIdParameter);
        }
    
        public virtual ObjectResult<VE_OPER_METHOD_SETTING> GET_OPER_METHOD_CONFIG(string siteId, Nullable<int> drId, Nullable<int> feId, Nullable<int> transTypeId, Nullable<int> catId, Nullable<int> roleId, Nullable<int> groupOperMethodId, Nullable<int> featureId, MergeOption mergeOption)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var drIdParameter = drId.HasValue ?
                new ObjectParameter("drId", drId) :
                new ObjectParameter("drId", typeof(int));
    
            var feIdParameter = feId.HasValue ?
                new ObjectParameter("feId", feId) :
                new ObjectParameter("feId", typeof(int));
    
            var transTypeIdParameter = transTypeId.HasValue ?
                new ObjectParameter("transTypeId", transTypeId) :
                new ObjectParameter("transTypeId", typeof(int));
    
            var catIdParameter = catId.HasValue ?
                new ObjectParameter("catId", catId) :
                new ObjectParameter("catId", typeof(int));
    
            var roleIdParameter = roleId.HasValue ?
                new ObjectParameter("roleId", roleId) :
                new ObjectParameter("roleId", typeof(int));
    
            var groupOperMethodIdParameter = groupOperMethodId.HasValue ?
                new ObjectParameter("groupOperMethodId", groupOperMethodId) :
                new ObjectParameter("groupOperMethodId", typeof(int));
    
            var featureIdParameter = featureId.HasValue ?
                new ObjectParameter("featureId", featureId) :
                new ObjectParameter("featureId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VE_OPER_METHOD_SETTING>("GET_OPER_METHOD_CONFIG", mergeOption, siteIdParameter, drIdParameter, feIdParameter, transTypeIdParameter, catIdParameter, roleIdParameter, groupOperMethodIdParameter, featureIdParameter);
        }
    
        public virtual ObjectResult<GET_TRANSACTION_TO_COMPARE_Result> GET_TRANSACTION_TO_COMPARE(string siteID, string partnerID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var partnerIDParameter = partnerID != null ?
                new ObjectParameter("PartnerID", partnerID) :
                new ObjectParameter("PartnerID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GET_TRANSACTION_TO_COMPARE_Result>("GET_TRANSACTION_TO_COMPARE", siteIDParameter, partnerIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_EPORT_CHARGE_LIST_Result> PR_GET_EPORT_CHARGE_LIST(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_EPORT_CHARGE_LIST_Result>("PR_GET_EPORT_CHARGE_LIST", fromDateParameter, toDateParameter);
        }
    
        public virtual ObjectResult<GET_EPORT_INVOICE_Result> GET_EPORT_INVOICE(string siteID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GET_EPORT_INVOICE_Result>("GET_EPORT_INVOICE", siteIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<GET_LIST_CONT_REGISTED_CARGO_Result> GET_LIST_CONT_REGISTED_CARGO(Nullable<int> pOrderId, string pItemNo, string pBookingNo, string pCargoId, Nullable<System.DateTime> pFromDate, Nullable<System.DateTime> pToDate, string pSiteId, Nullable<int> pCreatedBy)
        {
            var pOrderIdParameter = pOrderId.HasValue ?
                new ObjectParameter("pOrderId", pOrderId) :
                new ObjectParameter("pOrderId", typeof(int));
    
            var pItemNoParameter = pItemNo != null ?
                new ObjectParameter("pItemNo", pItemNo) :
                new ObjectParameter("pItemNo", typeof(string));
    
            var pBookingNoParameter = pBookingNo != null ?
                new ObjectParameter("pBookingNo", pBookingNo) :
                new ObjectParameter("pBookingNo", typeof(string));
    
            var pCargoIdParameter = pCargoId != null ?
                new ObjectParameter("pCargoId", pCargoId) :
                new ObjectParameter("pCargoId", typeof(string));
    
            var pFromDateParameter = pFromDate.HasValue ?
                new ObjectParameter("pFromDate", pFromDate) :
                new ObjectParameter("pFromDate", typeof(System.DateTime));
    
            var pToDateParameter = pToDate.HasValue ?
                new ObjectParameter("pToDate", pToDate) :
                new ObjectParameter("pToDate", typeof(System.DateTime));
    
            var pSiteIdParameter = pSiteId != null ?
                new ObjectParameter("pSiteId", pSiteId) :
                new ObjectParameter("pSiteId", typeof(string));
    
            var pCreatedByParameter = pCreatedBy.HasValue ?
                new ObjectParameter("pCreatedBy", pCreatedBy) :
                new ObjectParameter("pCreatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GET_LIST_CONT_REGISTED_CARGO_Result>("GET_LIST_CONT_REGISTED_CARGO", pOrderIdParameter, pItemNoParameter, pBookingNoParameter, pCargoIdParameter, pFromDateParameter, pToDateParameter, pSiteIdParameter, pCreatedByParameter);
        }
    
        public virtual ObjectResult<GET_LIST_CONT_REGISTED_DECL_Result> GET_LIST_CONT_REGISTED_DECL(Nullable<int> pOrderId, string pItemNo, string pBookingNo, string pCustDeclNo, Nullable<System.DateTime> pFromDate, Nullable<System.DateTime> pToDate, string pSiteId, string pValidated, Nullable<int> pCreatedBy)
        {
            var pOrderIdParameter = pOrderId.HasValue ?
                new ObjectParameter("pOrderId", pOrderId) :
                new ObjectParameter("pOrderId", typeof(int));
    
            var pItemNoParameter = pItemNo != null ?
                new ObjectParameter("pItemNo", pItemNo) :
                new ObjectParameter("pItemNo", typeof(string));
    
            var pBookingNoParameter = pBookingNo != null ?
                new ObjectParameter("pBookingNo", pBookingNo) :
                new ObjectParameter("pBookingNo", typeof(string));
    
            var pCustDeclNoParameter = pCustDeclNo != null ?
                new ObjectParameter("pCustDeclNo", pCustDeclNo) :
                new ObjectParameter("pCustDeclNo", typeof(string));
    
            var pFromDateParameter = pFromDate.HasValue ?
                new ObjectParameter("pFromDate", pFromDate) :
                new ObjectParameter("pFromDate", typeof(System.DateTime));
    
            var pToDateParameter = pToDate.HasValue ?
                new ObjectParameter("pToDate", pToDate) :
                new ObjectParameter("pToDate", typeof(System.DateTime));
    
            var pSiteIdParameter = pSiteId != null ?
                new ObjectParameter("pSiteId", pSiteId) :
                new ObjectParameter("pSiteId", typeof(string));
    
            var pValidatedParameter = pValidated != null ?
                new ObjectParameter("pValidated", pValidated) :
                new ObjectParameter("pValidated", typeof(string));
    
            var pCreatedByParameter = pCreatedBy.HasValue ?
                new ObjectParameter("pCreatedBy", pCreatedBy) :
                new ObjectParameter("pCreatedBy", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GET_LIST_CONT_REGISTED_DECL_Result>("GET_LIST_CONT_REGISTED_DECL", pOrderIdParameter, pItemNoParameter, pBookingNoParameter, pCustDeclNoParameter, pFromDateParameter, pToDateParameter, pSiteIdParameter, pValidatedParameter, pCreatedByParameter);
        }
    
        public virtual ObjectResult<GET_ORDERS_LIST_BY_METHOD_Result> PR_GET_ORDERS_LIST_BY_METHOD(string siteId, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, Nullable<int> userId, string operMethod, Nullable<bool> hasEDO, string registerFlag)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("userId", userId) :
                new ObjectParameter("userId", typeof(int));
    
            var operMethodParameter = operMethod != null ?
                new ObjectParameter("operMethod", operMethod) :
                new ObjectParameter("operMethod", typeof(string));
    
            var hasEDOParameter = hasEDO.HasValue ?
                new ObjectParameter("hasEDO", hasEDO) :
                new ObjectParameter("hasEDO", typeof(bool));
    
            var registerFlagParameter = registerFlag != null ?
                new ObjectParameter("registerFlag", registerFlag) :
                new ObjectParameter("registerFlag", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GET_ORDERS_LIST_BY_METHOD_Result>("PR_GET_ORDERS_LIST_BY_METHOD", siteIdParameter, fromDateParameter, toDateParameter, userIdParameter, operMethodParameter, hasEDOParameter, registerFlagParameter);
        }
    
        public virtual ObjectResult<VE_ORDER_DETAIL> GET_ORDER_DETAILS(string siteId, Nullable<int> orderId)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var orderIdParameter = orderId.HasValue ?
                new ObjectParameter("orderId", orderId) :
                new ObjectParameter("orderId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VE_ORDER_DETAIL>("GET_ORDER_DETAILS", siteIdParameter, orderIdParameter);
        }
    
        public virtual ObjectResult<VE_ORDER_DETAIL> GET_ORDER_DETAILS(string siteId, Nullable<int> orderId, MergeOption mergeOption)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var orderIdParameter = orderId.HasValue ?
                new ObjectParameter("orderId", orderId) :
                new ObjectParameter("orderId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<VE_ORDER_DETAIL>("GET_ORDER_DETAILS", mergeOption, siteIdParameter, orderIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_ERROR_Result> PR_GET_TRANSACTION_ERROR(string sITE_ID, string uSERNAME, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE, string pAYMENT_METHOD, string pAYMENT_STATUS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var uSERNAMEParameter = uSERNAME != null ?
                new ObjectParameter("USERNAME", uSERNAME) :
                new ObjectParameter("USERNAME", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            var pAYMENT_METHODParameter = pAYMENT_METHOD != null ?
                new ObjectParameter("PAYMENT_METHOD", pAYMENT_METHOD) :
                new ObjectParameter("PAYMENT_METHOD", typeof(string));
    
            var pAYMENT_STATUSParameter = pAYMENT_STATUS != null ?
                new ObjectParameter("PAYMENT_STATUS", pAYMENT_STATUS) :
                new ObjectParameter("PAYMENT_STATUS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_ERROR_Result>("PR_GET_TRANSACTION_ERROR", sITE_IDParameter, uSERNAMEParameter, fROMDATEParameter, tODATEParameter, pAYMENT_METHODParameter, pAYMENT_STATUSParameter);
        }
    
        public virtual ObjectResult<PR_GET_INVOICE_ERROR_Result> PR_GET_INVOICE_ERROR(string sITE_ID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE, string sERIAL, string iNVOICE_NO)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            var sERIALParameter = sERIAL != null ?
                new ObjectParameter("SERIAL", sERIAL) :
                new ObjectParameter("SERIAL", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_INVOICE_ERROR_Result>("PR_GET_INVOICE_ERROR", sITE_IDParameter, fROMDATEParameter, tODATEParameter, sERIALParameter, iNVOICE_NOParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPLACE_INVOICE_DETAIL_Result> PR_GET_REPLACE_INVOICE_DETAIL(string sITE_ID, string iNVOICE_FKEY)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var iNVOICE_FKEYParameter = iNVOICE_FKEY != null ?
                new ObjectParameter("INVOICE_FKEY", iNVOICE_FKEY) :
                new ObjectParameter("INVOICE_FKEY", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPLACE_INVOICE_DETAIL_Result>("PR_GET_REPLACE_INVOICE_DETAIL", sITE_IDParameter, iNVOICE_FKEYParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_REPORT_Result> PR_GET_TRANSACTION_REPORT(string sITE_ID, string uSER_NAME, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string iNVOICE_STATUS, string gATE_IN, Nullable<int> oRDER_ID, string oRDER_DETAIL_NO, string iNVOICE_NO, string tAX_FILE_NO, string sO_PHIEU, string oPER_METHOD, string pAID_BY_USER, string cREATEDBY, Nullable<int> hASEDO)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var uSER_NAMEParameter = uSER_NAME != null ?
                new ObjectParameter("USER_NAME", uSER_NAME) :
                new ObjectParameter("USER_NAME", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var iNVOICE_STATUSParameter = iNVOICE_STATUS != null ?
                new ObjectParameter("INVOICE_STATUS", iNVOICE_STATUS) :
                new ObjectParameter("INVOICE_STATUS", typeof(string));
    
            var gATE_INParameter = gATE_IN != null ?
                new ObjectParameter("GATE_IN", gATE_IN) :
                new ObjectParameter("GATE_IN", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var oRDER_DETAIL_NOParameter = oRDER_DETAIL_NO != null ?
                new ObjectParameter("ORDER_DETAIL_NO", oRDER_DETAIL_NO) :
                new ObjectParameter("ORDER_DETAIL_NO", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var sO_PHIEUParameter = sO_PHIEU != null ?
                new ObjectParameter("SO_PHIEU", sO_PHIEU) :
                new ObjectParameter("SO_PHIEU", typeof(string));
    
            var oPER_METHODParameter = oPER_METHOD != null ?
                new ObjectParameter("OPER_METHOD", oPER_METHOD) :
                new ObjectParameter("OPER_METHOD", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var cREATEDBYParameter = cREATEDBY != null ?
                new ObjectParameter("CREATEDBY", cREATEDBY) :
                new ObjectParameter("CREATEDBY", typeof(string));
    
            var hASEDOParameter = hASEDO.HasValue ?
                new ObjectParameter("HASEDO", hASEDO) :
                new ObjectParameter("HASEDO", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_REPORT_Result>("PR_GET_TRANSACTION_REPORT", sITE_IDParameter, uSER_NAMEParameter, fROM_DATEParameter, tO_DATEParameter, iNVOICE_STATUSParameter, gATE_INParameter, oRDER_IDParameter, oRDER_DETAIL_NOParameter, iNVOICE_NOParameter, tAX_FILE_NOParameter, sO_PHIEUParameter, oPER_METHODParameter, pAID_BY_USERParameter, cREATEDBYParameter, hASEDOParameter);
        }
    
        [DbFunction("OrderEntities", "GET_MERGED_SITEs")]
        public virtual IQueryable<string> GET_MERGED_SITEs(string siteId)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<string>("[OrderEntities].[GET_MERGED_SITEs](@siteId)", siteIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_REPORT_ADMIN_Result> PR_GET_TRANSACTION_REPORT_ADMIN(string sITE_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string iNVOICE_STATUS, string gATE_IN, Nullable<int> oRDER_ID, string oRDER_DETAIL_NO, string iNVOICE_NO, string tAX_FILE_NO, string sO_PHIEU, string oPER_METHOD, Nullable<int> hASEDO)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var iNVOICE_STATUSParameter = iNVOICE_STATUS != null ?
                new ObjectParameter("INVOICE_STATUS", iNVOICE_STATUS) :
                new ObjectParameter("INVOICE_STATUS", typeof(string));
    
            var gATE_INParameter = gATE_IN != null ?
                new ObjectParameter("GATE_IN", gATE_IN) :
                new ObjectParameter("GATE_IN", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var oRDER_DETAIL_NOParameter = oRDER_DETAIL_NO != null ?
                new ObjectParameter("ORDER_DETAIL_NO", oRDER_DETAIL_NO) :
                new ObjectParameter("ORDER_DETAIL_NO", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var sO_PHIEUParameter = sO_PHIEU != null ?
                new ObjectParameter("SO_PHIEU", sO_PHIEU) :
                new ObjectParameter("SO_PHIEU", typeof(string));
    
            var oPER_METHODParameter = oPER_METHOD != null ?
                new ObjectParameter("OPER_METHOD", oPER_METHOD) :
                new ObjectParameter("OPER_METHOD", typeof(string));
    
            var hASEDOParameter = hASEDO.HasValue ?
                new ObjectParameter("HASEDO", hASEDO) :
                new ObjectParameter("HASEDO", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_REPORT_ADMIN_Result>("PR_GET_TRANSACTION_REPORT_ADMIN", sITE_IDParameter, fROM_DATEParameter, tO_DATEParameter, iNVOICE_STATUSParameter, gATE_INParameter, oRDER_IDParameter, oRDER_DETAIL_NOParameter, iNVOICE_NOParameter, tAX_FILE_NOParameter, sO_PHIEUParameter, oPER_METHODParameter, hASEDOParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_REPORT_USER_Result> PR_GET_TRANSACTION_REPORT_USER(string sITE_ID, string uSER_NAME, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string iNVOICE_STATUS, string gATE_IN, Nullable<int> oRDER_ID, string oRDER_DETAIL_NO, string iNVOICE_NO, string tAX_FILE_NO, string sO_PHIEU, string oPER_METHOD, string pAID_BY_USER, string cREATEDBY, Nullable<int> hASEDO)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var uSER_NAMEParameter = uSER_NAME != null ?
                new ObjectParameter("USER_NAME", uSER_NAME) :
                new ObjectParameter("USER_NAME", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var iNVOICE_STATUSParameter = iNVOICE_STATUS != null ?
                new ObjectParameter("INVOICE_STATUS", iNVOICE_STATUS) :
                new ObjectParameter("INVOICE_STATUS", typeof(string));
    
            var gATE_INParameter = gATE_IN != null ?
                new ObjectParameter("GATE_IN", gATE_IN) :
                new ObjectParameter("GATE_IN", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var oRDER_DETAIL_NOParameter = oRDER_DETAIL_NO != null ?
                new ObjectParameter("ORDER_DETAIL_NO", oRDER_DETAIL_NO) :
                new ObjectParameter("ORDER_DETAIL_NO", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var sO_PHIEUParameter = sO_PHIEU != null ?
                new ObjectParameter("SO_PHIEU", sO_PHIEU) :
                new ObjectParameter("SO_PHIEU", typeof(string));
    
            var oPER_METHODParameter = oPER_METHOD != null ?
                new ObjectParameter("OPER_METHOD", oPER_METHOD) :
                new ObjectParameter("OPER_METHOD", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var cREATEDBYParameter = cREATEDBY != null ?
                new ObjectParameter("CREATEDBY", cREATEDBY) :
                new ObjectParameter("CREATEDBY", typeof(string));
    
            var hASEDOParameter = hASEDO.HasValue ?
                new ObjectParameter("HASEDO", hASEDO) :
                new ObjectParameter("HASEDO", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_REPORT_USER_Result>("PR_GET_TRANSACTION_REPORT_USER", sITE_IDParameter, uSER_NAMEParameter, fROM_DATEParameter, tO_DATEParameter, iNVOICE_STATUSParameter, gATE_INParameter, oRDER_IDParameter, oRDER_DETAIL_NOParameter, iNVOICE_NOParameter, tAX_FILE_NOParameter, sO_PHIEUParameter, oPER_METHODParameter, pAID_BY_USERParameter, cREATEDBYParameter, hASEDOParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTIONS_NOT_PUBLISH_INVOICE_Result> PR_GET_TRANSACTIONS_NOT_PUBLISH_INVOICE(string siteId, Nullable<int> oRDERID)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var oRDERIDParameter = oRDERID.HasValue ?
                new ObjectParameter("ORDERID", oRDERID) :
                new ObjectParameter("ORDERID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTIONS_NOT_PUBLISH_INVOICE_Result>("PR_GET_TRANSACTIONS_NOT_PUBLISH_INVOICE", siteIdParameter, oRDERIDParameter);
        }
    
        public virtual int PR_UPDATE_TRANSACTION_INFO(Nullable<int> transId, string transCode, string paymentRefNo, string paymentStatus)
        {
            var transIdParameter = transId.HasValue ?
                new ObjectParameter("TransId", transId) :
                new ObjectParameter("TransId", typeof(int));
    
            var transCodeParameter = transCode != null ?
                new ObjectParameter("TransCode", transCode) :
                new ObjectParameter("TransCode", typeof(string));
    
            var paymentRefNoParameter = paymentRefNo != null ?
                new ObjectParameter("PaymentRefNo", paymentRefNo) :
                new ObjectParameter("PaymentRefNo", typeof(string));
    
            var paymentStatusParameter = paymentStatus != null ?
                new ObjectParameter("PaymentStatus", paymentStatus) :
                new ObjectParameter("PaymentStatus", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("PR_UPDATE_TRANSACTION_INFO", transIdParameter, transCodeParameter, paymentRefNoParameter, paymentStatusParameter);
        }
    
        public virtual int sp_alterdiagram(string diagramname, Nullable<int> owner_id, Nullable<int> version, byte[] definition)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            var versionParameter = version.HasValue ?
                new ObjectParameter("version", version) :
                new ObjectParameter("version", typeof(int));
    
            var definitionParameter = definition != null ?
                new ObjectParameter("definition", definition) :
                new ObjectParameter("definition", typeof(byte[]));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_alterdiagram", diagramnameParameter, owner_idParameter, versionParameter, definitionParameter);
        }
    
        public virtual int sp_creatediagram(string diagramname, Nullable<int> owner_id, Nullable<int> version, byte[] definition)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            var versionParameter = version.HasValue ?
                new ObjectParameter("version", version) :
                new ObjectParameter("version", typeof(int));
    
            var definitionParameter = definition != null ?
                new ObjectParameter("definition", definition) :
                new ObjectParameter("definition", typeof(byte[]));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_creatediagram", diagramnameParameter, owner_idParameter, versionParameter, definitionParameter);
        }
    
        public virtual int sp_dropdiagram(string diagramname, Nullable<int> owner_id)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_dropdiagram", diagramnameParameter, owner_idParameter);
        }
    
        public virtual ObjectResult<sp_helpdiagramdefinition_Result> sp_helpdiagramdefinition(string diagramname, Nullable<int> owner_id)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<sp_helpdiagramdefinition_Result>("sp_helpdiagramdefinition", diagramnameParameter, owner_idParameter);
        }
    
        public virtual ObjectResult<sp_helpdiagrams_Result> sp_helpdiagrams(string diagramname, Nullable<int> owner_id)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<sp_helpdiagrams_Result>("sp_helpdiagrams", diagramnameParameter, owner_idParameter);
        }
    
        public virtual int sp_renamediagram(string diagramname, Nullable<int> owner_id, string new_diagramname)
        {
            var diagramnameParameter = diagramname != null ?
                new ObjectParameter("diagramname", diagramname) :
                new ObjectParameter("diagramname", typeof(string));
    
            var owner_idParameter = owner_id.HasValue ?
                new ObjectParameter("owner_id", owner_id) :
                new ObjectParameter("owner_id", typeof(int));
    
            var new_diagramnameParameter = new_diagramname != null ?
                new ObjectParameter("new_diagramname", new_diagramname) :
                new ObjectParameter("new_diagramname", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_renamediagram", diagramnameParameter, owner_idParameter, new_diagramnameParameter);
        }
    
        public virtual int sp_upgraddiagrams()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("sp_upgraddiagrams");
        }
    
        [DbFunction("OrderEntities", "SPLIT_STRING")]
        public virtual IQueryable<string> SPLIT_STRING(string p_STRING, string p_SPLIT_BY_CHAR)
        {
            var p_STRINGParameter = p_STRING != null ?
                new ObjectParameter("P_STRING", p_STRING) :
                new ObjectParameter("P_STRING", typeof(string));
    
            var p_SPLIT_BY_CHARParameter = p_SPLIT_BY_CHAR != null ?
                new ObjectParameter("P_SPLIT_BY_CHAR", p_SPLIT_BY_CHAR) :
                new ObjectParameter("P_SPLIT_BY_CHAR", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<string>("[OrderEntities].[SPLIT_STRING](@P_STRING, @P_SPLIT_BY_CHAR)", p_STRINGParameter, p_SPLIT_BY_CHARParameter);
        }
    
        public virtual ObjectResult<PR_GET_CHARGES_BY_SERIAL_INVOICE_NO_Result> PR_GET_CHARGES_BY_SERIAL_INVOICE_NO(string p_ORDER_ID, string p_SERIAL, string p_INV_NO)
        {
            var p_ORDER_IDParameter = p_ORDER_ID != null ?
                new ObjectParameter("P_ORDER_ID", p_ORDER_ID) :
                new ObjectParameter("P_ORDER_ID", typeof(string));
    
            var p_SERIALParameter = p_SERIAL != null ?
                new ObjectParameter("P_SERIAL", p_SERIAL) :
                new ObjectParameter("P_SERIAL", typeof(string));
    
            var p_INV_NOParameter = p_INV_NO != null ?
                new ObjectParameter("P_INV_NO", p_INV_NO) :
                new ObjectParameter("P_INV_NO", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_CHARGES_BY_SERIAL_INVOICE_NO_Result>("PR_GET_CHARGES_BY_SERIAL_INVOICE_NO", p_ORDER_IDParameter, p_SERIALParameter, p_INV_NOParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_INVOICE_PAYMENT_Result> PR_GET_TRANSACTION_INVOICE_PAYMENT(string sITE_ID, Nullable<int> oRDER_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string tAX_FILE_NO, string pAID_BY_USER, string iNVOICE_TYPE, string pATTERN, string sERIAL, string iNVOICE_NO, string mINUS_POINT_CUST_TYPE, string uSERLOGIN, string iNVSTTT, string fKEY, string mCCQT, string dOCSATT, string tBSS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var iNVOICE_TYPEParameter = iNVOICE_TYPE != null ?
                new ObjectParameter("INVOICE_TYPE", iNVOICE_TYPE) :
                new ObjectParameter("INVOICE_TYPE", typeof(string));
    
            var pATTERNParameter = pATTERN != null ?
                new ObjectParameter("PATTERN", pATTERN) :
                new ObjectParameter("PATTERN", typeof(string));
    
            var sERIALParameter = sERIAL != null ?
                new ObjectParameter("SERIAL", sERIAL) :
                new ObjectParameter("SERIAL", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var mINUS_POINT_CUST_TYPEParameter = mINUS_POINT_CUST_TYPE != null ?
                new ObjectParameter("MINUS_POINT_CUST_TYPE", mINUS_POINT_CUST_TYPE) :
                new ObjectParameter("MINUS_POINT_CUST_TYPE", typeof(string));
    
            var uSERLOGINParameter = uSERLOGIN != null ?
                new ObjectParameter("USERLOGIN", uSERLOGIN) :
                new ObjectParameter("USERLOGIN", typeof(string));
    
            var iNVSTTTParameter = iNVSTTT != null ?
                new ObjectParameter("INVSTTT", iNVSTTT) :
                new ObjectParameter("INVSTTT", typeof(string));
    
            var fKEYParameter = fKEY != null ?
                new ObjectParameter("FKEY", fKEY) :
                new ObjectParameter("FKEY", typeof(string));
    
            var mCCQTParameter = mCCQT != null ?
                new ObjectParameter("MCCQT", mCCQT) :
                new ObjectParameter("MCCQT", typeof(string));
    
            var dOCSATTParameter = dOCSATT != null ?
                new ObjectParameter("DOCSATT", dOCSATT) :
                new ObjectParameter("DOCSATT", typeof(string));
    
            var tBSSParameter = tBSS != null ?
                new ObjectParameter("TBSS", tBSS) :
                new ObjectParameter("TBSS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_INVOICE_PAYMENT_Result>("PR_GET_TRANSACTION_INVOICE_PAYMENT", sITE_IDParameter, oRDER_IDParameter, fROM_DATEParameter, tO_DATEParameter, tAX_FILE_NOParameter, pAID_BY_USERParameter, iNVOICE_TYPEParameter, pATTERNParameter, sERIALParameter, iNVOICE_NOParameter, mINUS_POINT_CUST_TYPEParameter, uSERLOGINParameter, iNVSTTTParameter, fKEYParameter, mCCQTParameter, dOCSATTParameter, tBSSParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_INVOICE_PAYMENT_ADMIN_Result> PR_GET_TRANSACTION_INVOICE_PAYMENT_ADMIN(string sITE_ID, Nullable<int> oRDER_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string tAX_FILE_NO, string pAID_BY_USER, string iNVOICE_TYPE, string pATTERN, string sERIAL, string iNVOICE_NO, string mINUS_POINT_CUST_TYPE, string uSERLOGIN, string iNVSTTT, string fKEY, string mCCQT, string dOCSATT, string tBSS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var iNVOICE_TYPEParameter = iNVOICE_TYPE != null ?
                new ObjectParameter("INVOICE_TYPE", iNVOICE_TYPE) :
                new ObjectParameter("INVOICE_TYPE", typeof(string));
    
            var pATTERNParameter = pATTERN != null ?
                new ObjectParameter("PATTERN", pATTERN) :
                new ObjectParameter("PATTERN", typeof(string));
    
            var sERIALParameter = sERIAL != null ?
                new ObjectParameter("SERIAL", sERIAL) :
                new ObjectParameter("SERIAL", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var mINUS_POINT_CUST_TYPEParameter = mINUS_POINT_CUST_TYPE != null ?
                new ObjectParameter("MINUS_POINT_CUST_TYPE", mINUS_POINT_CUST_TYPE) :
                new ObjectParameter("MINUS_POINT_CUST_TYPE", typeof(string));
    
            var uSERLOGINParameter = uSERLOGIN != null ?
                new ObjectParameter("USERLOGIN", uSERLOGIN) :
                new ObjectParameter("USERLOGIN", typeof(string));
    
            var iNVSTTTParameter = iNVSTTT != null ?
                new ObjectParameter("INVSTTT", iNVSTTT) :
                new ObjectParameter("INVSTTT", typeof(string));
    
            var fKEYParameter = fKEY != null ?
                new ObjectParameter("FKEY", fKEY) :
                new ObjectParameter("FKEY", typeof(string));
    
            var mCCQTParameter = mCCQT != null ?
                new ObjectParameter("MCCQT", mCCQT) :
                new ObjectParameter("MCCQT", typeof(string));
    
            var dOCSATTParameter = dOCSATT != null ?
                new ObjectParameter("DOCSATT", dOCSATT) :
                new ObjectParameter("DOCSATT", typeof(string));
    
            var tBSSParameter = tBSS != null ?
                new ObjectParameter("TBSS", tBSS) :
                new ObjectParameter("TBSS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_INVOICE_PAYMENT_ADMIN_Result>("PR_GET_TRANSACTION_INVOICE_PAYMENT_ADMIN", sITE_IDParameter, oRDER_IDParameter, fROM_DATEParameter, tO_DATEParameter, tAX_FILE_NOParameter, pAID_BY_USERParameter, iNVOICE_TYPEParameter, pATTERNParameter, sERIALParameter, iNVOICE_NOParameter, mINUS_POINT_CUST_TYPEParameter, uSERLOGINParameter, iNVSTTTParameter, fKEYParameter, mCCQTParameter, dOCSATTParameter, tBSSParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_INVOICE_PAYMENT_EXEC_Result> PR_GET_TRANSACTION_INVOICE_PAYMENT_EXEC(string sITE_ID, Nullable<int> oRDER_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string tAX_FILE_NO, string pAID_BY_USER, string iNVOICE_TYPE, string pATTERN, string sERIAL, string iNVOICE_NO, string mINUS_POINT_CUST_TYPE, string uSERLOGIN, string iNVSTTT, string fKEY, string mCCQT, string dOCSATT, string tBSS, string sEARCH_BY)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var iNVOICE_TYPEParameter = iNVOICE_TYPE != null ?
                new ObjectParameter("INVOICE_TYPE", iNVOICE_TYPE) :
                new ObjectParameter("INVOICE_TYPE", typeof(string));
    
            var pATTERNParameter = pATTERN != null ?
                new ObjectParameter("PATTERN", pATTERN) :
                new ObjectParameter("PATTERN", typeof(string));
    
            var sERIALParameter = sERIAL != null ?
                new ObjectParameter("SERIAL", sERIAL) :
                new ObjectParameter("SERIAL", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var mINUS_POINT_CUST_TYPEParameter = mINUS_POINT_CUST_TYPE != null ?
                new ObjectParameter("MINUS_POINT_CUST_TYPE", mINUS_POINT_CUST_TYPE) :
                new ObjectParameter("MINUS_POINT_CUST_TYPE", typeof(string));
    
            var uSERLOGINParameter = uSERLOGIN != null ?
                new ObjectParameter("USERLOGIN", uSERLOGIN) :
                new ObjectParameter("USERLOGIN", typeof(string));
    
            var iNVSTTTParameter = iNVSTTT != null ?
                new ObjectParameter("INVSTTT", iNVSTTT) :
                new ObjectParameter("INVSTTT", typeof(string));
    
            var fKEYParameter = fKEY != null ?
                new ObjectParameter("FKEY", fKEY) :
                new ObjectParameter("FKEY", typeof(string));
    
            var mCCQTParameter = mCCQT != null ?
                new ObjectParameter("MCCQT", mCCQT) :
                new ObjectParameter("MCCQT", typeof(string));
    
            var dOCSATTParameter = dOCSATT != null ?
                new ObjectParameter("DOCSATT", dOCSATT) :
                new ObjectParameter("DOCSATT", typeof(string));
    
            var tBSSParameter = tBSS != null ?
                new ObjectParameter("TBSS", tBSS) :
                new ObjectParameter("TBSS", typeof(string));
    
            var sEARCH_BYParameter = sEARCH_BY != null ?
                new ObjectParameter("SEARCH_BY", sEARCH_BY) :
                new ObjectParameter("SEARCH_BY", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_INVOICE_PAYMENT_EXEC_Result>("PR_GET_TRANSACTION_INVOICE_PAYMENT_EXEC", sITE_IDParameter, oRDER_IDParameter, fROM_DATEParameter, tO_DATEParameter, tAX_FILE_NOParameter, pAID_BY_USERParameter, iNVOICE_TYPEParameter, pATTERNParameter, sERIALParameter, iNVOICE_NOParameter, mINUS_POINT_CUST_TYPEParameter, uSERLOGINParameter, iNVSTTTParameter, fKEYParameter, mCCQTParameter, dOCSATTParameter, tBSSParameter, sEARCH_BYParameter);
        }
    
        public virtual ObjectResult<GET_AM_OPER_METHOD_SETTING_Result> GET_AM_OPER_METHOD_SETTING()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<GET_AM_OPER_METHOD_SETTING_Result>("GET_AM_OPER_METHOD_SETTING");
        }
    
        public virtual ObjectResult<PR_GET_ORDER_DETAIL_CHARGE_Result> PR_GET_ORDER_DETAIL_CHARGE(string siteId, Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, string chargeCodes)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var chargeCodesParameter = chargeCodes != null ?
                new ObjectParameter("chargeCodes", chargeCodes) :
                new ObjectParameter("chargeCodes", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_ORDER_DETAIL_CHARGE_Result>("PR_GET_ORDER_DETAIL_CHARGE", siteIdParameter, fromDateParameter, toDateParameter, chargeCodesParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_BY_ORDER_Result> PR_GET_TRANSACTION_BY_ORDER_DETAIL_ID(string siteId, Nullable<int> oRDERDETAIID)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var oRDERDETAIIDParameter = oRDERDETAIID.HasValue ?
                new ObjectParameter("ORDERDETAIID", oRDERDETAIID) :
                new ObjectParameter("ORDERDETAIID", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_BY_ORDER_Result>("PR_GET_TRANSACTION_BY_ORDER_DETAIL_ID", siteIdParameter, oRDERDETAIIDParameter);
        }
    
        public virtual ObjectResult<PR_GET_EPORT_LOYALTY_INVOICE_Result> PR_GET_EPORT_LOYALTY_INVOICE(string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE, string iNVOICE_NO, string cHARGE_CODE)
        {
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var cHARGE_CODEParameter = cHARGE_CODE != null ?
                new ObjectParameter("CHARGE_CODE", cHARGE_CODE) :
                new ObjectParameter("CHARGE_CODE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_EPORT_LOYALTY_INVOICE_Result>("PR_GET_EPORT_LOYALTY_INVOICE", sITEIDParameter, fROMDATEParameter, tODATEParameter, iNVOICE_NOParameter, cHARGE_CODEParameter);
        }
    
        public virtual ObjectResult<PR_GET_EPORT_INVOICE_EXACT_INTF_Result> PR_GET_EPORT_INVOICE_EXACT_INTF(string siteID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_EPORT_INVOICE_EXACT_INTF_Result>("PR_GET_EPORT_INVOICE_EXACT_INTF", siteIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_CUSTTAX_BY_ORDERDETAILNO_Result> PR_GET_CUSTTAX_BY_ORDERDETAILNO(string lstOrderDetailNo)
        {
            var lstOrderDetailNoParameter = lstOrderDetailNo != null ?
                new ObjectParameter("lstOrderDetailNo", lstOrderDetailNo) :
                new ObjectParameter("lstOrderDetailNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_CUSTTAX_BY_ORDERDETAILNO_Result>("PR_GET_CUSTTAX_BY_ORDERDETAILNO", lstOrderDetailNoParameter);
        }
    
        public virtual ObjectResult<PR_GET_AVAILBLE_ORDER_DETAILS_Result> PR_GET_AVAILBLE_ORDER_DETAILS(string sITE_ID, string oPER_METHOD, string iTEM_NO, string vES_ID, string sTATUS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oPER_METHODParameter = oPER_METHOD != null ?
                new ObjectParameter("OPER_METHOD", oPER_METHOD) :
                new ObjectParameter("OPER_METHOD", typeof(string));
    
            var iTEM_NOParameter = iTEM_NO != null ?
                new ObjectParameter("ITEM_NO", iTEM_NO) :
                new ObjectParameter("ITEM_NO", typeof(string));
    
            var vES_IDParameter = vES_ID != null ?
                new ObjectParameter("VES_ID", vES_ID) :
                new ObjectParameter("VES_ID", typeof(string));
    
            var sTATUSParameter = sTATUS != null ?
                new ObjectParameter("STATUS", sTATUS) :
                new ObjectParameter("STATUS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_AVAILBLE_ORDER_DETAILS_Result>("PR_GET_AVAILBLE_ORDER_DETAILS", sITE_IDParameter, oPER_METHODParameter, iTEM_NOParameter, vES_IDParameter, sTATUSParameter);
        }
    
        public virtual ObjectResult<PR_GET_AVAILBLE_ORDER_DETAILS_Result> PR_GET_AVAILBLE_ORDER_DETAILS_ITEMNO(string sITE_ID, string oPER_METHOD, string iTEM_NO, string vES_ID, string sTATUS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oPER_METHODParameter = oPER_METHOD != null ?
                new ObjectParameter("OPER_METHOD", oPER_METHOD) :
                new ObjectParameter("OPER_METHOD", typeof(string));
    
            var iTEM_NOParameter = iTEM_NO != null ?
                new ObjectParameter("ITEM_NO", iTEM_NO) :
                new ObjectParameter("ITEM_NO", typeof(string));
    
            var vES_IDParameter = vES_ID != null ?
                new ObjectParameter("VES_ID", vES_ID) :
                new ObjectParameter("VES_ID", typeof(string));
    
            var sTATUSParameter = sTATUS != null ?
                new ObjectParameter("STATUS", sTATUS) :
                new ObjectParameter("STATUS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_AVAILBLE_ORDER_DETAILS_Result>("PR_GET_AVAILBLE_ORDER_DETAILS_ITEMNO", sITE_IDParameter, oPER_METHODParameter, iTEM_NOParameter, vES_IDParameter, sTATUSParameter);
        }
    
        [DbFunction("OrderEntities", "SplitStr")]
        public virtual IQueryable<string> SplitStr(string convertedStr, string delimiter)
        {
            var convertedStrParameter = convertedStr != null ?
                new ObjectParameter("ConvertedStr", convertedStr) :
                new ObjectParameter("ConvertedStr", typeof(string));
    
            var delimiterParameter = delimiter != null ?
                new ObjectParameter("Delimiter", delimiter) :
                new ObjectParameter("Delimiter", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<string>("[OrderEntities].[SplitStr](@ConvertedStr, @Delimiter)", convertedStrParameter, delimiterParameter);
        }
    
        public virtual ObjectResult<PR_GET_CONTAINERS_SHIPPING_Result> PR_GET_CONTAINERS_SHIPPING(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, string placeOfReceive, string placeOfDelivery, string lineOper, string shippingService, string billOfLading, Nullable<int> orderId, string lineOperStatus, string customsStatus, string readyForTrans, string yardConsolStatus, string transSyncStatus, string transStatus, string taxNo)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("FromDate", fromDate) :
                new ObjectParameter("FromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("ToDate", toDate) :
                new ObjectParameter("ToDate", typeof(System.DateTime));
    
            var placeOfReceiveParameter = placeOfReceive != null ?
                new ObjectParameter("PlaceOfReceive", placeOfReceive) :
                new ObjectParameter("PlaceOfReceive", typeof(string));
    
            var placeOfDeliveryParameter = placeOfDelivery != null ?
                new ObjectParameter("PlaceOfDelivery", placeOfDelivery) :
                new ObjectParameter("PlaceOfDelivery", typeof(string));
    
            var lineOperParameter = lineOper != null ?
                new ObjectParameter("LineOper", lineOper) :
                new ObjectParameter("LineOper", typeof(string));
    
            var shippingServiceParameter = shippingService != null ?
                new ObjectParameter("ShippingService", shippingService) :
                new ObjectParameter("ShippingService", typeof(string));
    
            var billOfLadingParameter = billOfLading != null ?
                new ObjectParameter("BillOfLading", billOfLading) :
                new ObjectParameter("BillOfLading", typeof(string));
    
            var orderIdParameter = orderId.HasValue ?
                new ObjectParameter("OrderId", orderId) :
                new ObjectParameter("OrderId", typeof(int));
    
            var lineOperStatusParameter = lineOperStatus != null ?
                new ObjectParameter("LineOperStatus", lineOperStatus) :
                new ObjectParameter("LineOperStatus", typeof(string));
    
            var customsStatusParameter = customsStatus != null ?
                new ObjectParameter("CustomsStatus", customsStatus) :
                new ObjectParameter("CustomsStatus", typeof(string));
    
            var readyForTransParameter = readyForTrans != null ?
                new ObjectParameter("ReadyForTrans", readyForTrans) :
                new ObjectParameter("ReadyForTrans", typeof(string));
    
            var yardConsolStatusParameter = yardConsolStatus != null ?
                new ObjectParameter("YardConsolStatus", yardConsolStatus) :
                new ObjectParameter("YardConsolStatus", typeof(string));
    
            var transSyncStatusParameter = transSyncStatus != null ?
                new ObjectParameter("TransSyncStatus", transSyncStatus) :
                new ObjectParameter("TransSyncStatus", typeof(string));
    
            var transStatusParameter = transStatus != null ?
                new ObjectParameter("TransStatus", transStatus) :
                new ObjectParameter("TransStatus", typeof(string));
    
            var taxNoParameter = taxNo != null ?
                new ObjectParameter("TaxNo", taxNo) :
                new ObjectParameter("TaxNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_CONTAINERS_SHIPPING_Result>("PR_GET_CONTAINERS_SHIPPING", fromDateParameter, toDateParameter, placeOfReceiveParameter, placeOfDeliveryParameter, lineOperParameter, shippingServiceParameter, billOfLadingParameter, orderIdParameter, lineOperStatusParameter, customsStatusParameter, readyForTransParameter, yardConsolStatusParameter, transSyncStatusParameter, transStatusParameter, taxNoParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_INFO_TO_PUBLISH_INV_Result> PR_GET_TRANSACTION_INFO_TO_PUBLISH_INV(string sITE_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string iNVOICE_TYPE)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var iNVOICE_TYPEParameter = iNVOICE_TYPE != null ?
                new ObjectParameter("INVOICE_TYPE", iNVOICE_TYPE) :
                new ObjectParameter("INVOICE_TYPE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_INFO_TO_PUBLISH_INV_Result>("PR_GET_TRANSACTION_INFO_TO_PUBLISH_INV", sITE_IDParameter, fROM_DATEParameter, tO_DATEParameter, iNVOICE_TYPEParameter);
        }
    
        public virtual ObjectResult<PR_GET_INVOICE_DETAIL_REVENUE_REPORT_Result> PR_GET_INVOICE_DETAIL_REVENUE_REPORT(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, string paidSite, string pattern, string serial, string invoiceNo, string operMethod, string paymentMethod)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("FromDate", fromDate) :
                new ObjectParameter("FromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("ToDate", toDate) :
                new ObjectParameter("ToDate", typeof(System.DateTime));
    
            var paidSiteParameter = paidSite != null ?
                new ObjectParameter("PaidSite", paidSite) :
                new ObjectParameter("PaidSite", typeof(string));
    
            var patternParameter = pattern != null ?
                new ObjectParameter("Pattern", pattern) :
                new ObjectParameter("Pattern", typeof(string));
    
            var serialParameter = serial != null ?
                new ObjectParameter("Serial", serial) :
                new ObjectParameter("Serial", typeof(string));
    
            var invoiceNoParameter = invoiceNo != null ?
                new ObjectParameter("InvoiceNo", invoiceNo) :
                new ObjectParameter("InvoiceNo", typeof(string));
    
            var operMethodParameter = operMethod != null ?
                new ObjectParameter("OperMethod", operMethod) :
                new ObjectParameter("OperMethod", typeof(string));
    
            var paymentMethodParameter = paymentMethod != null ?
                new ObjectParameter("PaymentMethod", paymentMethod) :
                new ObjectParameter("PaymentMethod", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_INVOICE_DETAIL_REVENUE_REPORT_Result>("PR_GET_INVOICE_DETAIL_REVENUE_REPORT", fromDateParameter, toDateParameter, paidSiteParameter, patternParameter, serialParameter, invoiceNoParameter, operMethodParameter, paymentMethodParameter);
        }
    
        public virtual ObjectResult<PR_GET_INVOICE_REVENUE_REPORT_Result> PR_GET_INVOICE_REVENUE_REPORT(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, string paidSite, string pattern, string serial, string invoiceNo, string operMethod, string paymentMethod)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("FromDate", fromDate) :
                new ObjectParameter("FromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("ToDate", toDate) :
                new ObjectParameter("ToDate", typeof(System.DateTime));
    
            var paidSiteParameter = paidSite != null ?
                new ObjectParameter("PaidSite", paidSite) :
                new ObjectParameter("PaidSite", typeof(string));
    
            var patternParameter = pattern != null ?
                new ObjectParameter("Pattern", pattern) :
                new ObjectParameter("Pattern", typeof(string));
    
            var serialParameter = serial != null ?
                new ObjectParameter("Serial", serial) :
                new ObjectParameter("Serial", typeof(string));
    
            var invoiceNoParameter = invoiceNo != null ?
                new ObjectParameter("InvoiceNo", invoiceNo) :
                new ObjectParameter("InvoiceNo", typeof(string));
    
            var operMethodParameter = operMethod != null ?
                new ObjectParameter("OperMethod", operMethod) :
                new ObjectParameter("OperMethod", typeof(string));
    
            var paymentMethodParameter = paymentMethod != null ?
                new ObjectParameter("PaymentMethod", paymentMethod) :
                new ObjectParameter("PaymentMethod", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_INVOICE_REVENUE_REPORT_Result>("PR_GET_INVOICE_REVENUE_REPORT", fromDateParameter, toDateParameter, paidSiteParameter, patternParameter, serialParameter, invoiceNoParameter, operMethodParameter, paymentMethodParameter);
        }
    
        public virtual ObjectResult<PR_GET_ITEMNOS_BY_ORDERIDS_FORMITEMNOS_Result> PR_GET_ITEMNOS_BY_ORDERIDS_FORMITEMNOS(string afterItemNos)
        {
            var afterItemNosParameter = afterItemNos != null ?
                new ObjectParameter("AfterItemNos", afterItemNos) :
                new ObjectParameter("AfterItemNos", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_ITEMNOS_BY_ORDERIDS_FORMITEMNOS_Result>("PR_GET_ITEMNOS_BY_ORDERIDS_FORMITEMNOS", afterItemNosParameter);
        }
    
        public virtual ObjectResult<PR_GET_PAYMENT_LIST_LOGIN_Result> PR_GET_PAYMENT_LIST_LOGIN(string siteId, Nullable<int> orderId, string itemNo, string bill, string book)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var orderIdParameter = orderId.HasValue ?
                new ObjectParameter("OrderId", orderId) :
                new ObjectParameter("OrderId", typeof(int));
    
            var itemNoParameter = itemNo != null ?
                new ObjectParameter("ItemNo", itemNo) :
                new ObjectParameter("ItemNo", typeof(string));
    
            var billParameter = bill != null ?
                new ObjectParameter("Bill", bill) :
                new ObjectParameter("Bill", typeof(string));
    
            var bookParameter = book != null ?
                new ObjectParameter("Book", book) :
                new ObjectParameter("Book", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_PAYMENT_LIST_LOGIN_Result>("PR_GET_PAYMENT_LIST_LOGIN", siteIdParameter, orderIdParameter, itemNoParameter, billParameter, bookParameter);
        }
    
        public virtual ObjectResult<PR_GET_PAYMENT_LIST_NOT_LOGIN_Result> PR_GET_PAYMENT_LIST_NOT_LOGIN(string siteId, Nullable<int> orderId)
        {
            var siteIdParameter = siteId != null ?
                new ObjectParameter("SiteId", siteId) :
                new ObjectParameter("SiteId", typeof(string));
    
            var orderIdParameter = orderId.HasValue ?
                new ObjectParameter("OrderId", orderId) :
                new ObjectParameter("OrderId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_PAYMENT_LIST_NOT_LOGIN_Result>("PR_GET_PAYMENT_LIST_NOT_LOGIN", siteIdParameter, orderIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_INVOICES_BY_ORDERDETAILNO_Result> PR_GET_INVOICES_BY_ORDERDETAILNO(string lstOrderDetailNo, string siteId)
        {
            var lstOrderDetailNoParameter = lstOrderDetailNo != null ?
                new ObjectParameter("lstOrderDetailNo", lstOrderDetailNo) :
                new ObjectParameter("lstOrderDetailNo", typeof(string));
    
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_INVOICES_BY_ORDERDETAILNO_Result>("PR_GET_INVOICES_BY_ORDERDETAILNO", lstOrderDetailNoParameter, siteIdParameter);
        }
    
        public virtual ObjectResult<SP_ORDER_DETAIL_PORT_CHANGE_FILTER_BY_AGENT_Result> SP_ORDER_DETAIL_PORT_CHANGE_FILTER_BY_AGENT(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, string itemNos, string bookNumber, string registerCode, string tosConfirmStatus, string paymentStatus, string site, string freeStatus, string agent)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var itemNosParameter = itemNos != null ?
                new ObjectParameter("itemNos", itemNos) :
                new ObjectParameter("itemNos", typeof(string));
    
            var bookNumberParameter = bookNumber != null ?
                new ObjectParameter("bookNumber", bookNumber) :
                new ObjectParameter("bookNumber", typeof(string));
    
            var registerCodeParameter = registerCode != null ?
                new ObjectParameter("registerCode", registerCode) :
                new ObjectParameter("registerCode", typeof(string));
    
            var tosConfirmStatusParameter = tosConfirmStatus != null ?
                new ObjectParameter("tosConfirmStatus", tosConfirmStatus) :
                new ObjectParameter("tosConfirmStatus", typeof(string));
    
            var paymentStatusParameter = paymentStatus != null ?
                new ObjectParameter("paymentStatus", paymentStatus) :
                new ObjectParameter("paymentStatus", typeof(string));
    
            var siteParameter = site != null ?
                new ObjectParameter("site", site) :
                new ObjectParameter("site", typeof(string));
    
            var freeStatusParameter = freeStatus != null ?
                new ObjectParameter("freeStatus", freeStatus) :
                new ObjectParameter("freeStatus", typeof(string));
    
            var agentParameter = agent != null ?
                new ObjectParameter("agent", agent) :
                new ObjectParameter("agent", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SP_ORDER_DETAIL_PORT_CHANGE_FILTER_BY_AGENT_Result>("SP_ORDER_DETAIL_PORT_CHANGE_FILTER_BY_AGENT", fromDateParameter, toDateParameter, itemNosParameter, bookNumberParameter, registerCodeParameter, tosConfirmStatusParameter, paymentStatusParameter, siteParameter, freeStatusParameter, agentParameter);
        }
    
        public virtual ObjectResult<PR_GET_ORDER_DETAIL_PAID_Result> PR_GET_ORDER_DETAIL_PAID(string lstOrderDetailNo, string siteId)
        {
            var lstOrderDetailNoParameter = lstOrderDetailNo != null ?
                new ObjectParameter("lstOrderDetailNo", lstOrderDetailNo) :
                new ObjectParameter("lstOrderDetailNo", typeof(string));
    
            var siteIdParameter = siteId != null ?
                new ObjectParameter("siteId", siteId) :
                new ObjectParameter("siteId", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_ORDER_DETAIL_PAID_Result>("PR_GET_ORDER_DETAIL_PAID", lstOrderDetailNoParameter, siteIdParameter);
        }
    
        public virtual ObjectResult<PR_GET_REGISTER_DEBIT_ONLINE_Result> PR_GET_REGISTER_DEBIT_ONLINE(string siteID, string fORWARDERTAXCODE, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var fORWARDERTAXCODEParameter = fORWARDERTAXCODE != null ?
                new ObjectParameter("FORWARDERTAXCODE", fORWARDERTAXCODE) :
                new ObjectParameter("FORWARDERTAXCODE", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REGISTER_DEBIT_ONLINE_Result>("PR_GET_REGISTER_DEBIT_ONLINE", siteIDParameter, fORWARDERTAXCODEParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_PAYMENT_DEBIT_Result> PR_GET_TRANSACTION_PAYMENT_DEBIT()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_PAYMENT_DEBIT_Result>("PR_GET_TRANSACTION_PAYMENT_DEBIT");
        }
    
        public virtual ObjectResult<PR_GET_REGISTER_DEBIT_ONLINE_Result> PR_GET_REGISTER_DEBIT_ONLINE_BY_CHARGE_ID(string siteID, string chargeIds)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var chargeIdsParameter = chargeIds != null ?
                new ObjectParameter("chargeIds", chargeIds) :
                new ObjectParameter("chargeIds", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REGISTER_DEBIT_ONLINE_Result>("PR_GET_REGISTER_DEBIT_ONLINE_BY_CHARGE_ID", siteIDParameter, chargeIdsParameter);
        }
    
        public virtual ObjectResult<PR_GET_REGISTER_DEBIT_ONLINE_Result> PR_GET_REGISTER_DEBIT_ONLINE_BY_ORDER_DETAIL_NO(string siteID, string orderDetailNo)
        {
            var siteIDParameter = siteID != null ?
                new ObjectParameter("siteID", siteID) :
                new ObjectParameter("siteID", typeof(string));
    
            var orderDetailNoParameter = orderDetailNo != null ?
                new ObjectParameter("orderDetailNo", orderDetailNo) :
                new ObjectParameter("orderDetailNo", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REGISTER_DEBIT_ONLINE_Result>("PR_GET_REGISTER_DEBIT_ONLINE_BY_ORDER_DETAIL_NO", siteIDParameter, orderDetailNoParameter);
        }
    
        public virtual ObjectResult<SP_ORDER_DETAIL_PORT_CHANGE_FILTER_Result> SP_ORDER_DETAIL_PORT_CHANGE_FILTER(Nullable<System.DateTime> fromDate, Nullable<System.DateTime> toDate, string itemNos, string bookNumber, string registerCode, string tosConfirmStatus, string paymentStatus, string userIDs, string site, string freeStatus, string agent, string isAgent, string appliedChange)
        {
            var fromDateParameter = fromDate.HasValue ?
                new ObjectParameter("fromDate", fromDate) :
                new ObjectParameter("fromDate", typeof(System.DateTime));
    
            var toDateParameter = toDate.HasValue ?
                new ObjectParameter("toDate", toDate) :
                new ObjectParameter("toDate", typeof(System.DateTime));
    
            var itemNosParameter = itemNos != null ?
                new ObjectParameter("itemNos", itemNos) :
                new ObjectParameter("itemNos", typeof(string));
    
            var bookNumberParameter = bookNumber != null ?
                new ObjectParameter("bookNumber", bookNumber) :
                new ObjectParameter("bookNumber", typeof(string));
    
            var registerCodeParameter = registerCode != null ?
                new ObjectParameter("registerCode", registerCode) :
                new ObjectParameter("registerCode", typeof(string));
    
            var tosConfirmStatusParameter = tosConfirmStatus != null ?
                new ObjectParameter("tosConfirmStatus", tosConfirmStatus) :
                new ObjectParameter("tosConfirmStatus", typeof(string));
    
            var paymentStatusParameter = paymentStatus != null ?
                new ObjectParameter("paymentStatus", paymentStatus) :
                new ObjectParameter("paymentStatus", typeof(string));
    
            var userIDsParameter = userIDs != null ?
                new ObjectParameter("userIDs", userIDs) :
                new ObjectParameter("userIDs", typeof(string));
    
            var siteParameter = site != null ?
                new ObjectParameter("site", site) :
                new ObjectParameter("site", typeof(string));
    
            var freeStatusParameter = freeStatus != null ?
                new ObjectParameter("freeStatus", freeStatus) :
                new ObjectParameter("freeStatus", typeof(string));
    
            var agentParameter = agent != null ?
                new ObjectParameter("agent", agent) :
                new ObjectParameter("agent", typeof(string));
    
            var isAgentParameter = isAgent != null ?
                new ObjectParameter("isAgent", isAgent) :
                new ObjectParameter("isAgent", typeof(string));
    
            var appliedChangeParameter = appliedChange != null ?
                new ObjectParameter("appliedChange", appliedChange) :
                new ObjectParameter("appliedChange", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<SP_ORDER_DETAIL_PORT_CHANGE_FILTER_Result>("SP_ORDER_DETAIL_PORT_CHANGE_FILTER", fromDateParameter, toDateParameter, itemNosParameter, bookNumberParameter, registerCodeParameter, tosConfirmStatusParameter, paymentStatusParameter, userIDsParameter, siteParameter, freeStatusParameter, agentParameter, isAgentParameter, appliedChangeParameter);
        }
    
        public virtual ObjectResult<PR_GET_PORT_CHANGE_WITHOUT_APPLY_CHANGE_Result> PR_GET_PORT_CHANGE_WITHOUT_APPLY_CHANGE()
        {
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_PORT_CHANGE_WITHOUT_APPLY_CHANGE_Result>("PR_GET_PORT_CHANGE_WITHOUT_APPLY_CHANGE");
        }
    
        public virtual ObjectResult<PR_GET_PAYMENT_DETAIL_Result> PR_GET_PAYMENT_DETAIL(string sITE_ID, string tRANSACTION_CODE, string cHARGE_CODE)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var tRANSACTION_CODEParameter = tRANSACTION_CODE != null ?
                new ObjectParameter("TRANSACTION_CODE", tRANSACTION_CODE) :
                new ObjectParameter("TRANSACTION_CODE", typeof(string));
    
            var cHARGE_CODEParameter = cHARGE_CODE != null ?
                new ObjectParameter("CHARGE_CODE", cHARGE_CODE) :
                new ObjectParameter("CHARGE_CODE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_PAYMENT_DETAIL_Result>("PR_GET_PAYMENT_DETAIL", sITE_IDParameter, tRANSACTION_CODEParameter, cHARGE_CODEParameter);
        }
    
        public virtual ObjectResult<PR_GET_SERVICE_ATTACH_EXEC_Result> PR_GET_SERVICE_ATTACH_EXEC(string sITE_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, Nullable<int> uSER_ID, string oRDER_ID_NO, string eIR_ID, string bILLBOOK, string iTEM_NO, Nullable<int> oRDER_ID_SERVICE_ATT, string oRDER_NO_ATT_SERVICE, string aCCOMPANIED_SERVICE_CODE)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var uSER_IDParameter = uSER_ID.HasValue ?
                new ObjectParameter("USER_ID", uSER_ID) :
                new ObjectParameter("USER_ID", typeof(int));
    
            var oRDER_ID_NOParameter = oRDER_ID_NO != null ?
                new ObjectParameter("ORDER_ID_NO", oRDER_ID_NO) :
                new ObjectParameter("ORDER_ID_NO", typeof(string));
    
            var eIR_IDParameter = eIR_ID != null ?
                new ObjectParameter("EIR_ID", eIR_ID) :
                new ObjectParameter("EIR_ID", typeof(string));
    
            var bILLBOOKParameter = bILLBOOK != null ?
                new ObjectParameter("BILLBOOK", bILLBOOK) :
                new ObjectParameter("BILLBOOK", typeof(string));
    
            var iTEM_NOParameter = iTEM_NO != null ?
                new ObjectParameter("ITEM_NO", iTEM_NO) :
                new ObjectParameter("ITEM_NO", typeof(string));
    
            var oRDER_ID_SERVICE_ATTParameter = oRDER_ID_SERVICE_ATT.HasValue ?
                new ObjectParameter("ORDER_ID_SERVICE_ATT", oRDER_ID_SERVICE_ATT) :
                new ObjectParameter("ORDER_ID_SERVICE_ATT", typeof(int));
    
            var oRDER_NO_ATT_SERVICEParameter = oRDER_NO_ATT_SERVICE != null ?
                new ObjectParameter("ORDER_NO_ATT_SERVICE", oRDER_NO_ATT_SERVICE) :
                new ObjectParameter("ORDER_NO_ATT_SERVICE", typeof(string));
    
            var aCCOMPANIED_SERVICE_CODEParameter = aCCOMPANIED_SERVICE_CODE != null ?
                new ObjectParameter("ACCOMPANIED_SERVICE_CODE", aCCOMPANIED_SERVICE_CODE) :
                new ObjectParameter("ACCOMPANIED_SERVICE_CODE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_SERVICE_ATTACH_EXEC_Result>("PR_GET_SERVICE_ATTACH_EXEC", sITE_IDParameter, fROM_DATEParameter, tO_DATEParameter, uSER_IDParameter, oRDER_ID_NOParameter, eIR_IDParameter, bILLBOOKParameter, iTEM_NOParameter, oRDER_ID_SERVICE_ATTParameter, oRDER_NO_ATT_SERVICEParameter, aCCOMPANIED_SERVICE_CODEParameter);
        }
    
        public virtual ObjectResult<PR_GET_SERVICE_ATTACH_EXEC_ADMIN_Result> PR_GET_SERVICE_ATTACH_EXEC_ADMIN(string sITE_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string oRDER_ID_NO, string eIR_ID, string bILLBOOK, string iTEM_NO, Nullable<int> oRDER_ID_SERVICE_ATT, string oRDER_NO_ATT_SERVICE, string aCCOMPANIED_SERVICE_CODE)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var oRDER_ID_NOParameter = oRDER_ID_NO != null ?
                new ObjectParameter("ORDER_ID_NO", oRDER_ID_NO) :
                new ObjectParameter("ORDER_ID_NO", typeof(string));
    
            var eIR_IDParameter = eIR_ID != null ?
                new ObjectParameter("EIR_ID", eIR_ID) :
                new ObjectParameter("EIR_ID", typeof(string));
    
            var bILLBOOKParameter = bILLBOOK != null ?
                new ObjectParameter("BILLBOOK", bILLBOOK) :
                new ObjectParameter("BILLBOOK", typeof(string));
    
            var iTEM_NOParameter = iTEM_NO != null ?
                new ObjectParameter("ITEM_NO", iTEM_NO) :
                new ObjectParameter("ITEM_NO", typeof(string));
    
            var oRDER_ID_SERVICE_ATTParameter = oRDER_ID_SERVICE_ATT.HasValue ?
                new ObjectParameter("ORDER_ID_SERVICE_ATT", oRDER_ID_SERVICE_ATT) :
                new ObjectParameter("ORDER_ID_SERVICE_ATT", typeof(int));
    
            var oRDER_NO_ATT_SERVICEParameter = oRDER_NO_ATT_SERVICE != null ?
                new ObjectParameter("ORDER_NO_ATT_SERVICE", oRDER_NO_ATT_SERVICE) :
                new ObjectParameter("ORDER_NO_ATT_SERVICE", typeof(string));
    
            var aCCOMPANIED_SERVICE_CODEParameter = aCCOMPANIED_SERVICE_CODE != null ?
                new ObjectParameter("ACCOMPANIED_SERVICE_CODE", aCCOMPANIED_SERVICE_CODE) :
                new ObjectParameter("ACCOMPANIED_SERVICE_CODE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_SERVICE_ATTACH_EXEC_ADMIN_Result>("PR_GET_SERVICE_ATTACH_EXEC_ADMIN", sITE_IDParameter, fROM_DATEParameter, tO_DATEParameter, oRDER_ID_NOParameter, eIR_IDParameter, bILLBOOKParameter, iTEM_NOParameter, oRDER_ID_SERVICE_ATTParameter, oRDER_NO_ATT_SERVICEParameter, aCCOMPANIED_SERVICE_CODEParameter);
        }
    
        public virtual ObjectResult<PR_GET_SERVICE_ATTACH_EXEC_USER_Result> PR_GET_SERVICE_ATTACH_EXEC_USER(string sITE_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string oRDER_ID_NO, Nullable<int> uSER_ID, string eIR_ID, string bILLBOOK, string iTEM_NO, Nullable<int> oRDER_ID_SERVICE_ATT, string oRDER_NO_ATT_SERVICE, string aCCOMPANIED_SERVICE_CODE)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var oRDER_ID_NOParameter = oRDER_ID_NO != null ?
                new ObjectParameter("ORDER_ID_NO", oRDER_ID_NO) :
                new ObjectParameter("ORDER_ID_NO", typeof(string));
    
            var uSER_IDParameter = uSER_ID.HasValue ?
                new ObjectParameter("USER_ID", uSER_ID) :
                new ObjectParameter("USER_ID", typeof(int));
    
            var eIR_IDParameter = eIR_ID != null ?
                new ObjectParameter("EIR_ID", eIR_ID) :
                new ObjectParameter("EIR_ID", typeof(string));
    
            var bILLBOOKParameter = bILLBOOK != null ?
                new ObjectParameter("BILLBOOK", bILLBOOK) :
                new ObjectParameter("BILLBOOK", typeof(string));
    
            var iTEM_NOParameter = iTEM_NO != null ?
                new ObjectParameter("ITEM_NO", iTEM_NO) :
                new ObjectParameter("ITEM_NO", typeof(string));
    
            var oRDER_ID_SERVICE_ATTParameter = oRDER_ID_SERVICE_ATT.HasValue ?
                new ObjectParameter("ORDER_ID_SERVICE_ATT", oRDER_ID_SERVICE_ATT) :
                new ObjectParameter("ORDER_ID_SERVICE_ATT", typeof(int));
    
            var oRDER_NO_ATT_SERVICEParameter = oRDER_NO_ATT_SERVICE != null ?
                new ObjectParameter("ORDER_NO_ATT_SERVICE", oRDER_NO_ATT_SERVICE) :
                new ObjectParameter("ORDER_NO_ATT_SERVICE", typeof(string));
    
            var aCCOMPANIED_SERVICE_CODEParameter = aCCOMPANIED_SERVICE_CODE != null ?
                new ObjectParameter("ACCOMPANIED_SERVICE_CODE", aCCOMPANIED_SERVICE_CODE) :
                new ObjectParameter("ACCOMPANIED_SERVICE_CODE", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_SERVICE_ATTACH_EXEC_USER_Result>("PR_GET_SERVICE_ATTACH_EXEC_USER", sITE_IDParameter, fROM_DATEParameter, tO_DATEParameter, oRDER_ID_NOParameter, uSER_IDParameter, eIR_IDParameter, bILLBOOKParameter, iTEM_NOParameter, oRDER_ID_SERVICE_ATTParameter, oRDER_NO_ATT_SERVICEParameter, aCCOMPANIED_SERVICE_CODEParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_Result> PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE(string sITE_ID, Nullable<int> oRDER_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string tAX_FILE_NO, string pAID_BY_USER, string iNVOICE_TYPE, string pATTERN, string sERIAL, string iNVOICE_NO, string mINUS_POINT_CUST_TYPE, string uSERLOGIN, string iNVSTTT, string fKEY, string mCCQT, string dOCSATT, string tBSS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var iNVOICE_TYPEParameter = iNVOICE_TYPE != null ?
                new ObjectParameter("INVOICE_TYPE", iNVOICE_TYPE) :
                new ObjectParameter("INVOICE_TYPE", typeof(string));
    
            var pATTERNParameter = pATTERN != null ?
                new ObjectParameter("PATTERN", pATTERN) :
                new ObjectParameter("PATTERN", typeof(string));
    
            var sERIALParameter = sERIAL != null ?
                new ObjectParameter("SERIAL", sERIAL) :
                new ObjectParameter("SERIAL", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var mINUS_POINT_CUST_TYPEParameter = mINUS_POINT_CUST_TYPE != null ?
                new ObjectParameter("MINUS_POINT_CUST_TYPE", mINUS_POINT_CUST_TYPE) :
                new ObjectParameter("MINUS_POINT_CUST_TYPE", typeof(string));
    
            var uSERLOGINParameter = uSERLOGIN != null ?
                new ObjectParameter("USERLOGIN", uSERLOGIN) :
                new ObjectParameter("USERLOGIN", typeof(string));
    
            var iNVSTTTParameter = iNVSTTT != null ?
                new ObjectParameter("INVSTTT", iNVSTTT) :
                new ObjectParameter("INVSTTT", typeof(string));
    
            var fKEYParameter = fKEY != null ?
                new ObjectParameter("FKEY", fKEY) :
                new ObjectParameter("FKEY", typeof(string));
    
            var mCCQTParameter = mCCQT != null ?
                new ObjectParameter("MCCQT", mCCQT) :
                new ObjectParameter("MCCQT", typeof(string));
    
            var dOCSATTParameter = dOCSATT != null ?
                new ObjectParameter("DOCSATT", dOCSATT) :
                new ObjectParameter("DOCSATT", typeof(string));
    
            var tBSSParameter = tBSS != null ?
                new ObjectParameter("TBSS", tBSS) :
                new ObjectParameter("TBSS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_Result>("PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE", sITE_IDParameter, oRDER_IDParameter, fROM_DATEParameter, tO_DATEParameter, tAX_FILE_NOParameter, pAID_BY_USERParameter, iNVOICE_TYPEParameter, pATTERNParameter, sERIALParameter, iNVOICE_NOParameter, mINUS_POINT_CUST_TYPEParameter, uSERLOGINParameter, iNVSTTTParameter, fKEYParameter, mCCQTParameter, dOCSATTParameter, tBSSParameter);
        }
    
        public virtual ObjectResult<PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_ADMIN_Result> PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_ADMIN(string sITE_ID, Nullable<int> oRDER_ID, Nullable<System.DateTime> fROM_DATE, Nullable<System.DateTime> tO_DATE, string tAX_FILE_NO, string pAID_BY_USER, string iNVOICE_TYPE, string pATTERN, string sERIAL, string iNVOICE_NO, string mINUS_POINT_CUST_TYPE, string uSERLOGIN, string iNVSTTT, string fKEY, string mCCQT, string dOCSATT, string tBSS)
        {
            var sITE_IDParameter = sITE_ID != null ?
                new ObjectParameter("SITE_ID", sITE_ID) :
                new ObjectParameter("SITE_ID", typeof(string));
    
            var oRDER_IDParameter = oRDER_ID.HasValue ?
                new ObjectParameter("ORDER_ID", oRDER_ID) :
                new ObjectParameter("ORDER_ID", typeof(int));
    
            var fROM_DATEParameter = fROM_DATE.HasValue ?
                new ObjectParameter("FROM_DATE", fROM_DATE) :
                new ObjectParameter("FROM_DATE", typeof(System.DateTime));
    
            var tO_DATEParameter = tO_DATE.HasValue ?
                new ObjectParameter("TO_DATE", tO_DATE) :
                new ObjectParameter("TO_DATE", typeof(System.DateTime));
    
            var tAX_FILE_NOParameter = tAX_FILE_NO != null ?
                new ObjectParameter("TAX_FILE_NO", tAX_FILE_NO) :
                new ObjectParameter("TAX_FILE_NO", typeof(string));
    
            var pAID_BY_USERParameter = pAID_BY_USER != null ?
                new ObjectParameter("PAID_BY_USER", pAID_BY_USER) :
                new ObjectParameter("PAID_BY_USER", typeof(string));
    
            var iNVOICE_TYPEParameter = iNVOICE_TYPE != null ?
                new ObjectParameter("INVOICE_TYPE", iNVOICE_TYPE) :
                new ObjectParameter("INVOICE_TYPE", typeof(string));
    
            var pATTERNParameter = pATTERN != null ?
                new ObjectParameter("PATTERN", pATTERN) :
                new ObjectParameter("PATTERN", typeof(string));
    
            var sERIALParameter = sERIAL != null ?
                new ObjectParameter("SERIAL", sERIAL) :
                new ObjectParameter("SERIAL", typeof(string));
    
            var iNVOICE_NOParameter = iNVOICE_NO != null ?
                new ObjectParameter("INVOICE_NO", iNVOICE_NO) :
                new ObjectParameter("INVOICE_NO", typeof(string));
    
            var mINUS_POINT_CUST_TYPEParameter = mINUS_POINT_CUST_TYPE != null ?
                new ObjectParameter("MINUS_POINT_CUST_TYPE", mINUS_POINT_CUST_TYPE) :
                new ObjectParameter("MINUS_POINT_CUST_TYPE", typeof(string));
    
            var uSERLOGINParameter = uSERLOGIN != null ?
                new ObjectParameter("USERLOGIN", uSERLOGIN) :
                new ObjectParameter("USERLOGIN", typeof(string));
    
            var iNVSTTTParameter = iNVSTTT != null ?
                new ObjectParameter("INVSTTT", iNVSTTT) :
                new ObjectParameter("INVSTTT", typeof(string));
    
            var fKEYParameter = fKEY != null ?
                new ObjectParameter("FKEY", fKEY) :
                new ObjectParameter("FKEY", typeof(string));
    
            var mCCQTParameter = mCCQT != null ?
                new ObjectParameter("MCCQT", mCCQT) :
                new ObjectParameter("MCCQT", typeof(string));
    
            var dOCSATTParameter = dOCSATT != null ?
                new ObjectParameter("DOCSATT", dOCSATT) :
                new ObjectParameter("DOCSATT", typeof(string));
    
            var tBSSParameter = tBSS != null ?
                new ObjectParameter("TBSS", tBSS) :
                new ObjectParameter("TBSS", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_ADMIN_Result>("PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_ADMIN", sITE_IDParameter, oRDER_IDParameter, fROM_DATEParameter, tO_DATEParameter, tAX_FILE_NOParameter, pAID_BY_USERParameter, iNVOICE_TYPEParameter, pATTERNParameter, sERIALParameter, iNVOICE_NOParameter, mINUS_POINT_CUST_TYPEParameter, uSERLOGINParameter, iNVSTTTParameter, fKEYParameter, mCCQTParameter, dOCSATTParameter, tBSSParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPORT_CHECKIN_ONLINE_Result> PR_GET_REPORT_CHECKIN_ONLINE(string oPTION, string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var oPTIONParameter = oPTION != null ?
                new ObjectParameter("OPTION", oPTION) :
                new ObjectParameter("OPTION", typeof(string));
    
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPORT_CHECKIN_ONLINE_Result>("PR_GET_REPORT_CHECKIN_ONLINE", oPTIONParameter, sITEIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_Result> PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE(string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_Result>("PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE", sITEIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_Result> PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME(string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_Result>("PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME", sITEIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_V4_Result> PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_V4(string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_V4_Result>("PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_V4", sITEIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_V4_Result> PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_V4(string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_V4_Result>("PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_V4", sITEIDParameter, fROMDATEParameter, tODATEParameter);
        }
    
        public virtual ObjectResult<PR_GET_REPORT_CHECKIN_ONLINE_V4_Result> PR_GET_REPORT_CHECKIN_ONLINE_V4(string oPTION, string sITEID, Nullable<System.DateTime> fROMDATE, Nullable<System.DateTime> tODATE)
        {
            var oPTIONParameter = oPTION != null ?
                new ObjectParameter("OPTION", oPTION) :
                new ObjectParameter("OPTION", typeof(string));
    
            var sITEIDParameter = sITEID != null ?
                new ObjectParameter("SITEID", sITEID) :
                new ObjectParameter("SITEID", typeof(string));
    
            var fROMDATEParameter = fROMDATE.HasValue ?
                new ObjectParameter("FROMDATE", fROMDATE) :
                new ObjectParameter("FROMDATE", typeof(System.DateTime));
    
            var tODATEParameter = tODATE.HasValue ?
                new ObjectParameter("TODATE", tODATE) :
                new ObjectParameter("TODATE", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<PR_GET_REPORT_CHECKIN_ONLINE_V4_Result>("PR_GET_REPORT_CHECKIN_ONLINE_V4", oPTIONParameter, sITEIDParameter, fROMDATEParameter, tODATEParameter);
        }
    }
}
