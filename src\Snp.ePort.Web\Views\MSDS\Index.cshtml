@{
    ViewBag.Title = "Khai báo MSDS";

    var maxFileUpload = ViewBag.maxFileUpload ?? "5"; // để mặc định nếu không có
    var allowFileTypes = ViewBag.allowFileTypes ?? "*.pdf, *.txt, *.doc, *.docx, *.xls, *.xlsx"; // để mặc định nếu không có
    var maxFileSize = ViewBag.maxFileSize ?? "10"; // để mặc định nếu không có
}

<style>
    .upload-zone {
        position: relative;
    }

    .upload-area {
        transition: all 0.3s ease;
        min-height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .upload-area:hover {
        border-color: #0056b3 !important;
        background-color: #e3f2fd !important;
    }

    .upload-area.drag-over {
        border-color: #28a745 !important;
        background-color: #d4edda !important;
        transform: scale(1.02);
    }

    .file-item {
        background-color: #f8f9fa;
        transition: background-color 0.2s ease;
    }

    .file-item:hover {
        background-color: #e9ecef;
    }

    .upload-progress {
        margin-top: 10px;
    }

    .progress {
        height: 6px;
        border-radius: 3px;
    }

    .progress-bar {
        background: linear-gradient(90deg, #007bff, #0056b3);
        transition: width 0.3s ease;
    }

    .file-name {
        font-weight: 500;
        color: #495057;
    }

    .remove-file {
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }

    .remove-file:hover {
        opacity: 1;
    }

    #msdsTable th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }

    #msdsTable td {
        vertical-align: middle;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-group-actions {
        gap: 10px;
    }

    .alert-info-custom {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>


<div class="container-fluid">  
    <div class="row">
        <div class="col-12">
            @(Html.DevExtreme().DataGrid()
    .ID("lstContainerDangerous")
    .DataSource(new List<object>()) // hoặc gán khi load
    .KeyExpr("ORDER_DETAIL_ID")
    .ShowBorders(true)
    .ColumnAutoWidth(true)
    .Columns(columns =>
    {
        columns.Add().DataField("ITEM_NO").Caption("Số Container").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("ORDER_DETAIL_NO").Caption("Số đăng ký").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("BOOKING_NO").Caption("Số booking").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("HAZADOUS").Caption("IMO").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("UNNO").Caption("UNNO").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("HAZADOUS_COUNT").Caption("Số cặp IMO/UNNO").Alignment(HorizontalAlignment.Center);
        columns.Add()
            .Caption("Số Lượng MSDS đã import")
            .CalculateCellValue("function(data) { return 0; }")
            .Alignment(HorizontalAlignment.Center);

        columns.Add()
            .Caption("Tải lên MSDS")
            .Alignment(HorizontalAlignment.Center)
            .CellTemplate(@<text>
                <div class="upload-zone" data-container="<%- data.ORDER_DETAIL_NO %>">
                    <div class="upload-area"
                         style="border: 2px dashed #007bff; padding: 10px; text-align: center; cursor: pointer; border-radius: 8px; background-color: #f8f9fa;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 18px; color: #007bff;"></i>
                        <p class="mb-1" style="font-size: 12px;">Kéo & thả hoặc nhấn để chọn file</p>
                        <input type="file" name="files" multiple hidden class="upload-input"
                               accept=".pdf,.txt,.doc,.docx,.xls,.xlsx" />
                    </div>
                    <div class="file-list mt-1"></div>
                    <div class="upload-progress mt-1" style="display: none;">
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">Đang upload...</small>
                    </div>
                </div>
            </text>);
    }))
        </div>
    </div>
</div>

<div class="d-flex justify-content-between align-items-start mt-3 flex-wrap">
    <div class="text-danger mb-2" style="max-width: 65%;">
        <b>Lưu ý khi khai báo MSDS:</b><br />
        - Container chứa hàng nguy hiểm bắt buộc phải đính kèm file MSDS khi giao hàng vào cảng.<br />
        - Mỗi cặp IMO/UNNO cần có ít nhất 1 file MSDS tương ứng.<br />
        - Chỉ chấp nhận các file định dạng: <span class="text-danger mb-2">@allowFileTypes</span><br />
        - Tổng dung lượng file MSDS cho mỗi container không vượt quá <span class="text-danger mb-2">@maxFileUpload MB</span>.
    </div>

    <div class="text-end">
        <button id="importBtn" class="btn btn-info me-2 mb-2">Import MSDS</button>
        <button id="saveBtn" class="btn btn-primary mb-2">Lưu</button>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Load cấu hình từ server
            loadMSDSConfig();

            // Xử lý sự kiện click vào vùng upload
            $('.upload-area').on('click', function () {
                $(this).find('.upload-input').click();
            });

            // Xử lý sự kiện chọn file
            $('.upload-input').on('change', function () {
                handleFileSelection(this);
            });

            // Xử lý drag & drop
            $('.upload-area').on('dragover', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('drag-over');
            });

            $('.upload-area').on('dragleave', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('drag-over');
            });

            $('.upload-area').on('drop', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('drag-over');

                const files = e.originalEvent.dataTransfer.files;
                const input = $(this).find('.upload-input')[0];
                input.files = files;
                handleFileSelection(input);
            });

            // Xử lý checkbox "Chọn tất cả"
            $('#checkAll').on('change', function () {
                $('input[name="selected"]').prop('checked', this.checked);
            });

            // Xử lý nút Import MSDS
            $('#importBtn').on('click', function () {
                uploadSelectedFiles();
            });

            // Xử lý nút Lưu
            $('#saveBtn').on('click', function () {
                saveAllChanges();
            });
        });

        function loadMSDSConfig() {
            $.ajax({
                url: '/MSDS/GetConfig',
                type: 'GET',
                success: function (config) {
                    if (config) {
                        msdsConfig.maxFilesPerContainer = config.MaxFilesPerContainer || 5;
                        msdsConfig.maxTotalSizePerContainer = config.MaxTotalSizePerContainer || (10 * 1024 * 1024);
                        msdsConfig.allowedExtensions = config.AllowedExtensions || msdsConfig.allowedExtensions;
                        msdsConfig.allowedExtensionsDisplay = config.AllowedExtensionsDisplay || msdsConfig.allowedExtensionsDisplay;
                    }
                },
                error: function () {
                    console.warn('Không thể tải cấu hình MSDS, sử dụng cấu hình mặc định');
                }
            });
        }

        function handleFileSelection(input) {
            const container = $(input).closest('.upload-zone').data('container');
            const currentFiles = parseInt($(input).closest('.upload-zone').data('current-files')) || 0;
            const files = Array.from(input.files);
            const fileList = $(input).closest('.upload-zone').find('.file-list');

            // Validate files
            const validation = validateFiles(files, currentFiles);
            if (!validation.isValid) {
                showError(validation.errors.join('<br>'));
                input.value = ''; // Clear input
                return;
            }

            // Hiển thị danh sách file
            displayFileList(fileList, files, container);
        }

        function validateFiles(files, currentFileCount) {
            const result = { isValid: true, errors: [] };

            // Kiểm tra số lượng file
            if (files.length > msdsConfig.maxFilesPerContainer) {
                result.isValid = false;
                result.errors.push(`Chỉ được chọn tối đa ${msdsConfig.maxFilesPerContainer} file cùng lúc.`);
            }

            // Kiểm tra định dạng file
            const invalidFiles = files.filter(file => {
                const ext = '.' + file.name.split('.').pop().toLowerCase();
                return !msdsConfig.allowedExtensions.includes(ext);
            });

            if (invalidFiles.length > 0) {
                result.isValid = false;
                result.errors.push(`Hệ thống chỉ chấp nhận các định dạng file sau: ${msdsConfig.allowedExtensionsDisplay}. Vui lòng kiểm tra định dạng trước khi tải lên.`);
            }

            // Kiểm tra tổng dung lượng
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            if (totalSize > msdsConfig.maxTotalSizePerContainer) {
                result.isValid = false;
                const maxSizeMB = (msdsConfig.maxTotalSizePerContainer / 1024 / 1024).toFixed(0);
                result.errors.push(`Tổng dung lượng file MSDS cho 1 container tối đa ${maxSizeMB}MB. Vui lòng kiểm tra trước khi tải lên.`);
            }

            return result;
        }

        function displayFileList(fileList, files, container) {
            fileList.empty();

            files.forEach((file, index) => {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const fileItem = $(`
                    <div class="file-item d-flex justify-content-between align-items-center p-2 border rounded mb-1" data-file-index="${index}">
                        <div>
                            <i class="fas fa-file-alt me-2"></i>
                            <span class="file-name">${file.name}</span>
                            <small class="text-muted ms-2">(${fileSize} MB)</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-file-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                fileList.append(fileItem);
            });

            // Xử lý xóa file
            fileList.find('.remove-file').on('click', function () {
                const fileIndex = $(this).data('file-index');
                removeFileFromInput($(this).closest('.upload-zone').find('.upload-input')[0], fileIndex);
            });
        }

        function removeFileFromInput(input, indexToRemove) {
            const dt = new DataTransfer();
            const files = Array.from(input.files);

            files.forEach((file, index) => {
                if (index !== indexToRemove) {
                    dt.items.add(file);
                }
            });

            input.files = dt.files;
            handleFileSelection(input);
        }

        function uploadSelectedFiles() {
            const selectedContainers = $('input[name="selected"]:checked');

            if (selectedContainers.length === 0) {
                showError('Vui lòng chọn ít nhất một container để upload MSDS.');
                return;
            }

            const uploadPromises = [];

            selectedContainers.each(function () {
                const container = $(this).val();
                const uploadZone = $(`.upload-zone[data-container="${container}"]`);
                const input = uploadZone.find('.upload-input')[0];

                if (input.files.length > 0) {
                    uploadPromises.push(uploadFilesForContainer(container, input.files, uploadZone));
                }
            });

            if (uploadPromises.length === 0) {
                showError('Không có file nào được chọn để upload.');
                return;
            }

            Promise.all(uploadPromises)
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const totalCount = results.length;

                    if (successCount === totalCount) {
                        showSuccess(`Upload thành công ${successCount} container.`);
                        // Refresh page hoặc update UI
                        location.reload();
                    } else {
                        showWarning(`Upload thành công ${successCount}/${totalCount} container. Vui lòng kiểm tra lại.`);
                    }
                })
                .catch(error => {
                    showError('Có lỗi xảy ra trong quá trình upload: ' + error.message);
                });
        }

        function uploadFilesForContainer(container, files, uploadZone) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('containerNo', container);

                Array.from(files).forEach((file, index) => {
                    formData.append(`files`, file);
                });

                // Hiển thị progress
                const progressDiv = uploadZone.find('.upload-progress');
                const progressBar = progressDiv.find('.progress-bar');
                progressDiv.show();

                $.ajax({
                    url: '/MSDS/UploadFiles',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function () {
                        const xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                const percentComplete = (evt.loaded / evt.total) * 100;
                                progressBar.css('width', percentComplete + '%');
                            }
                        }, false);
                        return xhr;
                    },
                    success: function (response) {
                        progressDiv.hide();
                        if (response.Success) {
                            resolve({ success: true, container: container });
                            // Clear input
                            uploadZone.find('.upload-input').val('');
                            uploadZone.find('.file-list').empty();
                        } else {
                            showError(`Container ${container}: ${response.Message}`);
                            resolve({ success: false, container: container, error: response.Message });
                        }
                    },
                    error: function (xhr, status, error) {
                        progressDiv.hide();
                        const errorMsg = xhr.responseJSON?.Message || error || 'Upload thất bại';
                        showError(`Container ${container}: ${errorMsg}`);
                        resolve({ success: false, container: container, error: errorMsg });
                    }
                });
            });
        }

        function saveAllChanges() {
            // Implement save logic if needed
            showSuccess('Đã lưu thành công!');
        }

        function showError(msg) {
            // Sử dụng thư viện notification có sẵn trong dự án ePort
            if (typeof message !== 'undefined' && message.error) {
                message.error(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.error(msg);
            } else {
                alert('Lỗi: ' + msg);
            }
        }

        function showSuccess(msg) {
            if (typeof message !== 'undefined' && message.success) {
                message.success(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.success(msg);
            } else {
                alert('Thành công: ' + msg);
            }
        }

        function showWarning(msg) {
            if (typeof message !== 'undefined' && message.warning) {
                message.warning(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.warning(msg);
            } else {
                alert('Cảnh báo: ' + msg);
            }
        }

        function loadExistingFiles(uploadZone) {
            var orderDetailNo = uploadZone.data('order-detail');

            $.ajax({
                url: '@Url.Action("GetFilesByOrderDetail", "MSDS")',
                type: 'GET',
                data: { orderDetailNo: orderDetailNo },
                success: function(files) {
                    var fileList = uploadZone.find('.file-list');
                    fileList.empty();

                    if (files && files.length > 0) {
                        files.forEach(function(file) {
                            var fileItem = $('<div class="file-item d-flex justify-content-between align-items-center p-2 mb-1 border rounded">' +
                                '<div>' +
                                    '<span class="file-name">' + file.FileName + '</span>' +
                                    '<small class="text-muted ms-2">(' + formatFileSize(file.FileSize) + ')</small>' +
                                '</div>' +
                                '<button type="button" class="btn btn-sm btn-outline-danger remove-file" data-file-id="' + file.FileId + '">' +
                                    '<i class="fas fa-times"></i>' +
                                '</button>' +
                            '</div>');
                            fileList.append(fileItem);
                        });
                    }
                },
                error: function() {
                    console.log('Không thể tải danh sách file hiện có');
                }
            });
        }

        function deleteFile(fileId, uploadZone) {
            if (!confirm('Bạn có chắc chắn muốn xóa file này?')) {
                return;
            }

            $.ajax({
                url: '@Url.Action("DeleteFile", "MSDS")',
                type: 'POST',
                data: { fileId: fileId },
                success: function(response) {
                    if (response.Success) {
                        showSuccess(response.Message);
                        loadExistingFiles(uploadZone);
                        // Update MSDS count
                        var containerNo = uploadZone.data('container');
                        var currentCount = parseInt(uploadZone.data('current-files')) || 0;
                        updateMSDSCount(containerNo, Math.max(0, currentCount - 1));
                    } else {
                        showError(response.Message);
                    }
                },
                error: function() {
                    showError('Lỗi khi xóa file');
                }
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function updateMSDSCount(containerNo, newCount) {
            // Update the MSDS count display for the container
            const row = $(`input[value="${containerNo}"]`).closest('tr');
            const countCell = row.find('td:nth-child(8)');
            const badge = countCell.find('.badge');

            if (badge.length > 0) {
                badge.text(newCount);
                badge.removeClass('bg-warning bg-success');
                badge.addClass(newCount > 0 ? 'bg-success' : 'bg-warning');
            }

            // Update data attribute
            $(`.upload-zone[data-container="${containerNo}"]`).attr('data-current-files', newCount);
        }

        // Load existing files when page loads
        $(document).ready(function() {
            $('.upload-zone').each(function() {
                loadExistingFiles($(this));
            });

            // Handle delete file button click
            $(document).on('click', '.remove-file', function() {
                var fileId = $(this).data('file-id');
                var uploadZone = $(this).closest('.upload-zone');
                deleteFile(fileId, uploadZone);
            });
        });

        // CSS cho drag & drop
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .upload-area.drag-over {
                    border-color: #28a745 !important;
                    background-color: #d4edda !important;
                }
                .file-item {
                    background-color: #f8f9fa;
                }
                .file-item:hover {
                    background-color: #e9ecef;
                }
            `)
            .appendTo('head');
    </script>
}
