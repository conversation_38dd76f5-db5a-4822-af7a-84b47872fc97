@{
    ViewBag.Title = "Khai báo MSDS";

    var maxFileUpload = ViewBag.maxFileUpload ?? "5"; // để mặc định nếu không có
    var allowFileTypes = ViewBag.allowFileTypes ?? "*.pdf, *.txt, *.doc, *.docx, *.xls, *.xlsx"; // để mặc định nếu không có
    var maxFileSize = ViewBag.maxFileSize ?? "10"; // để mặc định nếu không có
}

<style>
    .upload-zone {
        position: relative;
    }

    .upload-area {
        transition: all 0.3s ease;
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .upload-area:hover {
        border-color: #0056b3 !important;
        background-color: #e3f2fd !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    }

    .upload-area.drag-over {
        border-color: #28a745 !important;
        background-color: #d4edda !important;
        transform: scale(1.02);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.2);
    }

    .upload-area.drag-over::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(40, 167, 69, 0.1) 50%, transparent 70%);
        animation: shimmer 1.5s infinite;
    }

    .file-item {
        background-color: #f8f9fa;
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        margin-bottom: 6px;
        animation: slideInUp 0.3s ease;
    }

    .file-item:hover {
        background-color: #e9ecef;
        border-color: #dee2e6;
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .upload-progress {
        margin-top: 10px;
        padding: 8px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .progress {
        height: 8px;
        border-radius: 4px;
        background-color: #e9ecef;
        overflow: hidden;
        position: relative;
    }

    .progress-bar {
        background: linear-gradient(90deg, #007bff, #0056b3);
        transition: width 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: progressShine 1.5s infinite;
    }

    @@keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    @@keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes progressShine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .file-name {
        font-weight: 500;
        color: #495057;
        display: flex;
        align-items: center;
    }

    .file-name i {
        margin-right: 8px;
        color: #6c757d;
    }

    .remove-file {
        opacity: 0.7;
        transition: all 0.2s ease;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-file:hover {
        opacity: 1;
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        transform: scale(1.1);
    }

    #msdsTable th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }

    #msdsTable td {
        vertical-align: middle;
    }

    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-group-actions {
        gap: 10px;
    }

    .alert-info-custom {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Responsive design */
    @@media (max-width: 768px) {
        .upload-area {
            min-height: 80px;
            padding: 10px;
        }

        .upload-area i {
            font-size: 20px !important;
        }

        .file-item {
            padding: 8px !important;
        }

        .file-item .file-info {
            font-size: 12px;
        }

        .remove-file {
            width: 24px;
            height: 24px;
        }
    }

    /* Success state */
    .upload-area.upload-success {
        border-color: #28a745;
        background-color: #d4edda;
    }

    .upload-area.upload-success i {
        color: #28a745;
    }

    /* Error state */
    .upload-area.upload-error {
        border-color: #dc3545;
        background-color: #f8d7da;
    }

    .upload-area.upload-error i {
        color: #dc3545;
    }

    /* File type specific styling */
    .file-item.file-pdf { border-left: 4px solid #dc3545; }
    .file-item.file-word { border-left: 4px solid #2b579a; }
    .file-item.file-excel { border-left: 4px solid #217346; }
    .file-item.file-text { border-left: 4px solid #6c757d; }
</style>


<div class="container-fluid">  
    <div class="row">
        <div class="col-12">
            @(Html.DevExtreme().DataGrid()
    .ID("lstContainerDangerous")
    .DataSource(new List<object>()) // hoặc gán khi load
    .KeyExpr("ORDER_DETAIL_ID")
    .ShowBorders(true)
    .ColumnAutoWidth(true)
    .Selection(s =>
    {
        s.Mode(SelectionMode.Multiple);
        s.ShowCheckBoxesMode(GridSelectionShowCheckBoxesMode.Always);
        s.AllowSelectAll(true);
    })
    .Columns(columns =>
    {
        columns.Add().DataField("ITEM_NO").Caption("Số Container").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("ORDER_DETAIL_NO").Caption("Số đăng ký").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("BOOKING_NO").Caption("Số booking").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("HAZADOUS").Caption("IMO").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("UNNO").Caption("UNNO").Alignment(HorizontalAlignment.Center);
        columns.Add().DataField("HAZADOUS_COUNT").Caption("Số cặp IMO/UNNO").Alignment(HorizontalAlignment.Center);
        columns.Add()
            .Caption("Số Lượng MSDS đã import")
            .CalculateCellValue("function(data) { return 0; }")
            .Alignment(HorizontalAlignment.Center);

        columns.Add()
            .Caption("Tải lên MSDS")
            .Alignment(HorizontalAlignment.Center)
            .CellTemplate(@<text>
                <div class="upload-zone" data-container="<%- data.ORDER_DETAIL_NO %>">
                    <div class="upload-area"
                         style="border: 2px dashed #007bff; padding: 15px; text-align: center; cursor: pointer; border-radius: 8px; background-color: #f8f9fa; min-height: 100px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 24px; color: #007bff; margin-bottom: 8px;"></i>
                        <p class="mb-1" style="font-size: 13px; font-weight: 500; color: #495057;">
                            <i class="fas fa-hand-pointer" style="font-size: 12px; margin-right: 4px;"></i>
                            Nhấn hoặc kéo thả để chọn file
                        </p>
                        <small class="text-muted" style="font-size: 11px;">
                            Tối đa 5 file • PDF, TXT, DOC, DOCX, XLS, XLSX • 10MB
                        </small>
                        <input type="file" name="files" multiple hidden class="upload-input"
                               accept=".pdf,.txt,.doc,.docx,.xls,.xlsx" />
                    </div>
                    <div class="file-list mt-2"></div>
                    <div class="upload-progress mt-2" style="display: none;">
                        <div class="progress" style="height: 6px; border-radius: 3px;">
                            <div class="progress-bar" role="progressbar" style="width: 0%; background: linear-gradient(90deg, #007bff, #0056b3);"></div>
                        </div>
                        <small class="text-muted mt-1 d-block">
                            <i class="fas fa-spinner fa-spin me-1"></i>
                            Đang upload...
                        </small>
                    </div>
                </div>
            </text>);
    }))
        </div>
    </div>
</div>

<div class="d-flex justify-content-between align-items-start mt-3 flex-wrap">
    <div class="text-danger mb-2" style="max-width: 65%;">
        <b>Lưu ý khi khai báo MSDS:</b><br />
        - Container chứa hàng nguy hiểm bắt buộc phải đính kèm file MSDS khi giao hàng vào cảng.<br />
        - Mỗi cặp IMO/UNNO cần có ít nhất 1 file MSDS tương ứng.<br />
        - Chỉ chấp nhận các file định dạng: <span class="text-danger mb-2">@allowFileTypes</span><br />
        - Tổng dung lượng file MSDS cho mỗi container không vượt quá <span class="text-danger mb-2">@maxFileUpload MB</span>.
    </div>

    <div class="text-end">
        <button id="importBtn" class="btn btn-info me-2 mb-2">Import MSDS</button>
        <button id="saveBtn" class="btn btn-primary mb-2">Lưu</button>
    </div>
</div>

@section Scripts {
    <script>
        // Cấu hình MSDS mặc định
        var msdsConfig = {
            maxFilesPerContainer: 5,
            maxTotalSizePerContainer: 10 * 1024 * 1024, // 10MB
            allowedExtensions: ['.pdf', '.txt', '.doc', '.docx', '.xls', '.xlsx'],
            allowedExtensionsDisplay: '*.pdf, *.txt, *.doc, *.docx, *.xls, *.xlsx'
        };

        $(document).ready(function () {
            // Load cấu hình từ server
            loadMSDSConfig();

            // Xử lý sự kiện click vào vùng upload
            $(document).on('click', '.upload-area', function () {
                $(this).find('.upload-input').click();
            });

            // Xử lý sự kiện chọn file
            $(document).on('change', '.upload-input', function () {
                handleFileSelection(this);
            });

            // Xử lý drag & drop
            $(document).on('dragover', '.upload-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('drag-over');
            });

            $(document).on('dragleave', '.upload-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('drag-over');
            });

            $(document).on('drop', '.upload-area', function (e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('drag-over');

                const files = e.originalEvent.dataTransfer.files;
                const input = $(this).find('.upload-input')[0];
                input.files = files;
                handleFileSelection(input);
            });

            // Xử lý checkbox "Chọn tất cả"
            $('#checkAll').on('change', function () {
                $('input[name="selected"]').prop('checked', this.checked);
            });

            // Xử lý nút Import MSDS
            $('#importBtn').on('click', function () {
                uploadSelectedFiles();
            });

            // Xử lý nút Lưu
            $('#saveBtn').on('click', function () {
                saveAllChanges();
            });
        });

        function loadMSDSConfig() {
            $.ajax({
                url: '/MSDS/GetConfig',
                type: 'GET',
                success: function (config) {
                    if (config) {
                        msdsConfig.maxFilesPerContainer = config.MaxFilesPerContainer || 5;
                        msdsConfig.maxTotalSizePerContainer = config.MaxTotalSizePerContainer || (10 * 1024 * 1024);
                        msdsConfig.allowedExtensions = config.AllowedExtensions || msdsConfig.allowedExtensions;
                        msdsConfig.allowedExtensionsDisplay = config.AllowedExtensionsDisplay || msdsConfig.allowedExtensionsDisplay;
                    }
                },
                error: function () {
                    console.warn('Không thể tải cấu hình MSDS, sử dụng cấu hình mặc định');
                }
            });
        }

        function handleFileSelection(input) {
            const container = $(input).closest('.upload-zone').data('container');
            const currentFiles = parseInt($(input).closest('.upload-zone').data('current-files')) || 0;
            const files = Array.from(input.files);
            const fileList = $(input).closest('.upload-zone').find('.file-list');
            const uploadArea = $(input).closest('.upload-zone').find('.upload-area');

            // Reset upload area state
            uploadArea.removeClass('upload-success upload-error');

            // Validate files
            const validation = validateFiles(files, currentFiles);
            if (!validation.isValid) {
                uploadArea.addClass('upload-error');
                setTimeout(() => {
                    uploadArea.removeClass('upload-error');
                }, 3000);

                showError(validation.errors.join('<br>'));
                input.value = ''; // Clear input
                return;
            }

            // Show success feedback for valid files
            if (files.length > 0) {
                uploadArea.addClass('upload-success');
                setTimeout(() => {
                    uploadArea.removeClass('upload-success');
                }, 1500);
            }

            // Hiển thị danh sách file
            displayFileList(fileList, files, container);
        }

        function validateFiles(files, currentFileCount) {
            const result = { isValid: true, errors: [] };

            // Kiểm tra số lượng file
            if (files.length > msdsConfig.maxFilesPerContainer) {
                result.isValid = false;
                result.errors.push(`Chỉ được chọn tối đa ${msdsConfig.maxFilesPerContainer} file cùng lúc.`);
            }

            // Kiểm tra định dạng file
            const invalidFiles = files.filter(file => {
                const ext = '.' + file.name.split('.').pop().toLowerCase();
                return !msdsConfig.allowedExtensions.includes(ext);
            });

            if (invalidFiles.length > 0) {
                result.isValid = false;
                result.errors.push(`Hệ thống chỉ chấp nhận các định dạng file sau: ${msdsConfig.allowedExtensionsDisplay}. Vui lòng kiểm tra định dạng trước khi tải lên.`);
            }

            // Kiểm tra tổng dung lượng
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            if (totalSize > msdsConfig.maxTotalSizePerContainer) {
                result.isValid = false;
                const maxSizeMB = (msdsConfig.maxTotalSizePerContainer / 1024 / 1024).toFixed(0);
                result.errors.push(`Tổng dung lượng file MSDS cho 1 container tối đa ${maxSizeMB}MB. Vui lòng kiểm tra trước khi tải lên.`);
            }

            return result;
        }

        function displayFileList(fileList, files, container) {
            fileList.empty();

            files.forEach((file, index) => {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const fileExtension = file.name.split('.').pop().toLowerCase();
                const fileIcon = getFileIcon(fileExtension);
                const fileTypeClass = getFileTypeClass(fileExtension);

                const fileItem = $(`
                    <div class="file-item ${fileTypeClass} d-flex justify-content-between align-items-center p-2" data-file-index="${index}">
                        <div class="d-flex align-items-center flex-grow-1">
                            <i class="${fileIcon} me-2" style="font-size: 16px; color: ${getFileColor(fileExtension)};"></i>
                            <div class="file-info">
                                <div class="file-name">${file.name}</div>
                                <small class="text-muted">${fileSize} MB • ${fileExtension.toUpperCase()}</small>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-file-index="${index}" title="Xóa file">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);

                fileList.append(fileItem);
            });

            // Xử lý xóa file
            fileList.find('.remove-file').on('click', function () {
                const fileIndex = $(this).data('file-index');
                removeFileFromInput($(this).closest('.upload-zone').find('.upload-input')[0], fileIndex);
            });
        }

        function getFileIcon(extension) {
            const iconMap = {
                'pdf': 'fas fa-file-pdf',
                'doc': 'fas fa-file-word',
                'docx': 'fas fa-file-word',
                'xls': 'fas fa-file-excel',
                'xlsx': 'fas fa-file-excel',
                'txt': 'fas fa-file-alt'
            };
            return iconMap[extension] || 'fas fa-file';
        }

        function getFileColor(extension) {
            const colorMap = {
                'pdf': '#dc3545',
                'doc': '#2b579a',
                'docx': '#2b579a',
                'xls': '#217346',
                'xlsx': '#217346',
                'txt': '#6c757d'
            };
            return colorMap[extension] || '#6c757d';
        }

        function getFileTypeClass(extension) {
            const classMap = {
                'pdf': 'file-pdf',
                'doc': 'file-word',
                'docx': 'file-word',
                'xls': 'file-excel',
                'xlsx': 'file-excel',
                'txt': 'file-text'
            };
            return classMap[extension] || 'file-other';
        }

        function removeFileFromInput(input, indexToRemove) {
            const dt = new DataTransfer();
            const files = Array.from(input.files);

            files.forEach((file, index) => {
                if (index !== indexToRemove) {
                    dt.items.add(file);
                }
            });

            input.files = dt.files;
            handleFileSelection(input);
        }

        function uploadSelectedFiles() {
            const selectedContainers = $('input[name="selected"]:checked');

            if (selectedContainers.length === 0) {
                showError('Vui lòng chọn ít nhất một container để upload MSDS.');
                return;
            }

            const uploadPromises = [];

            selectedContainers.each(function () {
                const container = $(this).val();
                const uploadZone = $(`.upload-zone[data-container="${container}"]`);
                const input = uploadZone.find('.upload-input')[0];

                if (input.files.length > 0) {
                    uploadPromises.push(uploadFilesForContainer(container, input.files, uploadZone));
                }
            });

            if (uploadPromises.length === 0) {
                showError('Không có file nào được chọn để upload.');
                return;
            }

            Promise.all(uploadPromises)
                .then(results => {
                    const successCount = results.filter(r => r.success).length;
                    const totalCount = results.length;

                    if (successCount === totalCount) {
                        showSuccess(`Upload thành công ${successCount} container.`);
                        // Refresh page hoặc update UI
                        location.reload();
                    } else {
                        showWarning(`Upload thành công ${successCount}/${totalCount} container. Vui lòng kiểm tra lại.`);
                    }
                })
                .catch(error => {
                    showError('Có lỗi xảy ra trong quá trình upload: ' + error.message);
                });
        }

        function uploadFilesForContainer(container, files, uploadZone) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('containerNo', container);

                Array.from(files).forEach((file, index) => {
                    formData.append(`files`, file);
                });

                // Hiển thị progress
                const progressDiv = uploadZone.find('.upload-progress');
                const progressBar = progressDiv.find('.progress-bar');
                progressDiv.show();

                $.ajax({
                    url: '/MSDS/UploadFiles',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function () {
                        const xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                const percentComplete = (evt.loaded / evt.total) * 100;
                                progressBar.css('width', percentComplete + '%');
                            }
                        }, false);
                        return xhr;
                    },
                    success: function (response) {
                        progressDiv.hide();
                        if (response.Success) {
                            // Show success state
                            uploadZone.find('.upload-area').addClass('upload-success').removeClass('upload-error');
                            setTimeout(() => {
                                uploadZone.find('.upload-area').removeClass('upload-success');
                            }, 2000);

                            resolve({ success: true, container: container });
                            // Clear input
                            uploadZone.find('.upload-input').val('');
                            uploadZone.find('.file-list').empty();

                            // Show success message with file count
                            const fileCount = uploadZone.find('.file-list .file-item').length;
                            showSuccess(`Upload thành công ${fileCount} file cho container ${container}`);
                        } else {
                            // Show error state
                            uploadZone.find('.upload-area').addClass('upload-error').removeClass('upload-success');
                            setTimeout(() => {
                                uploadZone.find('.upload-area').removeClass('upload-error');
                            }, 3000);

                            showError(`Container ${container}: ${response.Message}`);
                            resolve({ success: false, container: container, error: response.Message });
                        }
                    },
                    error: function (xhr, status, error) {
                        progressDiv.hide();
                        const errorMsg = xhr.responseJSON?.Message || error || 'Upload thất bại';
                        showError(`Container ${container}: ${errorMsg}`);
                        resolve({ success: false, container: container, error: errorMsg });
                    }
                });
            });
        }

        function saveAllChanges() {
            // Implement save logic if needed
            showSuccess('Đã lưu thành công!');
        }

        function showError(msg) {
            // Sử dụng thư viện notification có sẵn trong dự án ePort
            if (typeof message !== 'undefined' && message.error) {
                message.error(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.error(msg);
            } else {
                alert('Lỗi: ' + msg);
            }
        }

        function showSuccess(msg) {
            if (typeof message !== 'undefined' && message.success) {
                message.success(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.success(msg);
            } else {
                alert('Thành công: ' + msg);
            }
        }

        function showWarning(msg) {
            if (typeof message !== 'undefined' && message.warning) {
                message.warning(msg);
            } else if (typeof toastr !== 'undefined') {
                toastr.warning(msg);
            } else {
                alert('Cảnh báo: ' + msg);
            }
        }

        function loadExistingFiles(uploadZone) {
            var orderDetailNo = uploadZone.data('order-detail');

            $.ajax({
                url: '@Url.Action("GetFilesByOrderDetail", "MSDS")',
                type: 'GET',
                data: { orderDetailNo: orderDetailNo },
                success: function(files) {
                    var fileList = uploadZone.find('.file-list');
                    fileList.empty();

                    if (files && files.length > 0) {
                        files.forEach(function(file) {
                            var fileItem = $('<div class="file-item d-flex justify-content-between align-items-center p-2 mb-1 border rounded">' +
                                '<div>' +
                                    '<span class="file-name">' + file.FileName + '</span>' +
                                    '<small class="text-muted ms-2">(' + formatFileSize(file.FileSize) + ')</small>' +
                                '</div>' +
                                '<button type="button" class="btn btn-sm btn-outline-danger remove-file" data-file-id="' + file.FileId + '">' +
                                    '<i class="fas fa-times"></i>' +
                                '</button>' +
                            '</div>');
                            fileList.append(fileItem);
                        });
                    }
                },
                error: function() {
                    console.log('Không thể tải danh sách file hiện có');
                }
            });
        }

        function deleteFile(fileId, uploadZone) {
            if (!confirm('Bạn có chắc chắn muốn xóa file này?')) {
                return;
            }

            $.ajax({
                url: '@Url.Action("DeleteFile", "MSDS")',
                type: 'POST',
                data: { fileId: fileId },
                success: function(response) {
                    if (response.Success) {
                        showSuccess(response.Message);
                        loadExistingFiles(uploadZone);
                        // Update MSDS count
                        var containerNo = uploadZone.data('container');
                        var currentCount = parseInt(uploadZone.data('current-files')) || 0;
                        updateMSDSCount(containerNo, Math.max(0, currentCount - 1));
                    } else {
                        showError(response.Message);
                    }
                },
                error: function() {
                    showError('Lỗi khi xóa file');
                }
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function updateMSDSCount(containerNo, newCount) {
            // Update the MSDS count display for the container
            const row = $(`input[value="${containerNo}"]`).closest('tr');
            const countCell = row.find('td:nth-child(8)');
            const badge = countCell.find('.badge');

            if (badge.length > 0) {
                badge.text(newCount);
                badge.removeClass('bg-warning bg-success');
                badge.addClass(newCount > 0 ? 'bg-success' : 'bg-warning');
            }

            // Update data attribute
            $(`.upload-zone[data-container="${containerNo}"]`).attr('data-current-files', newCount);
        }

        // Load existing files when page loads
        $(document).ready(function() {
            $('.upload-zone').each(function() {
                loadExistingFiles($(this));
            });

            // Handle delete file button click
            $(document).on('click', '.remove-file', function() {
                var fileId = $(this).data('file-id');
                var uploadZone = $(this).closest('.upload-zone');
                deleteFile(fileId, uploadZone);
            });
        });

        // CSS cho drag & drop
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .upload-area.drag-over {
                    border-color: #28a745 !important;
                    background-color: #d4edda !important;
                }
                .file-item {
                    background-color: #f8f9fa;
                }
                .file-item:hover {
                    background-color: #e9ecef;
                }
            `)
            .appendTo('head');
    </script>
}
