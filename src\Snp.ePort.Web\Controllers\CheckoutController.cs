using AutoMapper;
using Newtonsoft.Json;
using Snp.Cache;
using Snp.ePort.Common;
using Snp.ePort.Common.Utilities;
using Snp.ePort.Core.Exceptions;
using Snp.ePort.Entity;
using Snp.ePort.Entity.DTO;
using Snp.ePort.Entity.DTO.Billing;
using Snp.ePort.Entity.DTO.Checkout;
using Snp.ePort.Entity.DTO.Loyalty;
using Snp.ePort.Entity.DTO.Napas;
using Snp.ePort.Gateway;
using Snp.ePort.Report;
using Snp.ePort.Web.Infrastructure.Authentication.Principal;
using Snp.ePort.Web.Models.NganLuong;
using Snp.ePort.Web.Pages.Extension;
using Snp.ePort.Web.Sessions;
using Snp.Log;
using Snp.Log.Utils;
using Snp.Tos.Entity;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Snp.ePort.Web.Controllers
{
    public class CheckoutController : CommonController
    {
        #region Field

        private readonly ITOSCategoryGateway _tOsCategoryGateway;
        private readonly IOrderGateway _orderGateway;
        private readonly IConfigGateway _configGateway;
        private readonly IEportBillingGateway _eportBillingGateway;
        private readonly IBillingGateway _billingGateway;
        private readonly IAuthGateway _authGateway;
        private readonly ISmsGateway _smsGateway;
        private readonly ICategoryGateway _categoryGateway;

        #endregion Field

        #region Ctor

        public CheckoutController(
            ITOSCategoryGateway tosCategoryGateway,
            IOrderGateway orderGateway,
            IConfigGateway configGateway,
            IEportBillingGateway eportBillingGateway,
            IBillingGateway billingGateway,
            IAuthGateway authGateway,
            ISmsGateway smsGateway,
            ICacheManager cacheManager,
            ICategoryGateway categoryGateway
            ) : base(cacheManager)
        {
            _tOsCategoryGateway = tosCategoryGateway;
            _orderGateway = orderGateway;
            _configGateway = configGateway;
            _billingGateway = billingGateway;
            _eportBillingGateway = eportBillingGateway;
            _authGateway = authGateway;
            _smsGateway = smsGateway;
            _categoryGateway = categoryGateway;
        }

        #endregion Ctor

        #region Properties

        private string _siteId = string.Empty;

        private string GetSiteId()
        {
            if (string.IsNullOrEmpty(_siteId))
            {
                _siteId = User != null ? LoggedSiteId : BillingSession.Current.BillingHelpSiteId;
            }
            return _siteId;
        }

        private AM_USER _userCheckOut = null;

        private AM_USER UserCheckout()
        {
            if (_userCheckOut == null)
            {
                _userCheckOut = User != null ? new AM_USER
                {
                    USER_ID = User.UserId,
                    USER_NAME = User.Username,
                    POST_PAID_FLG = string.Empty
                } : GetEPortUser();
            }
            return _userCheckOut;
        }

        private string GetTaxFileNo()
        {
            if (User != null)
            {
                return User.TaxFileNo;
            }
            return string.Empty;
        }

        private List<int> ListOrderDetails => BillingSession.Current.OrderDetailIds;

        private int OrderId => BillingSession.Current.OrderId;

        private MerchantInfo MerchantInfo => BillingSession.Current.MerchantInfo;
        private NganLuongMerchantInfo NganLuongMerchantInfo => BillingSession.Current.NganLuongMerchantInfo;

        #endregion Properties

        #region Page Load

        public ActionResult Index(int? oid = null, string pid = null)
        {
            List<ORDER_DETAIL> ods = null;
            if (!ValidateOrderId(GetSiteId(), pid, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, CommonUtilities.GetTransId(), ref ods))
            {
                throw new ePortException("Phiên làm việc này đã hết hạn.");
            }

            return RedirectToAction("Cart", "Checkout", new { oid, pid });
        }


        // GET: Checkout
        public ActionResult Cart(int? oid = null, string pid = null)
        {
            CartDto cart = new CartDto();
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (string.IsNullOrEmpty(GetSiteId()))
                {
                    return RedirectToAction("Index", "Home");
                }

                cart.Pid = pid;

                if (oid == null || oid.Value < 1)
                {
                    oid = OrderId;
                }

                if (oid == 0)
                {
                    ViewBag.Error = "Không tìm thấy thông tin lô hàng.";
                    return View(cart);
                }

                List<ORDER_DETAIL> ods = null;
                if (!ValidateOrderId(GetSiteId(), pid, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId, ref ods))
                {
                    SerilogHelper.WriteBugLogic(GetSiteId(), $"ValidateOrderId - pid : {pid} - ORDER_DETAIL: {ods?.ToJson()}", typeof(CheckoutController), "Cart", transId);
                    throw new ePortException("Xảy ra lỗi trong quá trình thanh toán, vui lòng thử lại.");
                }

                if (ListOrderDetails == null || ListOrderDetails.Count == 0)
                {
                    ViewBag.Error = "Không tìm thấy thông tin danh sách đăng ký.";
                    return View(cart);
                }

                // Lấy thông tin lô hàng
                var order = _orderGateway.GetOrder(GetSiteId(), oid.Value, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                if (order == null)
                {
                    ViewBag.Error = string.Format("Không tìm thấy mã lô {0} trong hệ thống.", oid);
                    return View(cart);
                }

                //TOS-8660
                string taxFileNo = GetTaxFileNo();
                if (!string.IsNullOrEmpty(order.CUST_TAXFILENO))
                {
                    taxFileNo = order.CUST_TAXFILENO;
                }

                cart.OrderId = oid.Value;
                cart.Customer = GetCustomerInfo(GetSiteId(), taxFileNo, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                if (User != null)
                {
                    if (CommonWeb.CheckPostPaid(User))
                    {
                        cart.PostPaidFlg = BOOLEANTYPE.Yes;
                    }
                }

                string siteId = GetSiteId();
                var cfgPaymentNL = _configGateway.GetePortSetting(siteId, AmEportSettingKey.SiteApplyPaymentNL, GatewayAuthInfor.USER,
                    GatewayAuthInfor.PASSWORD, transId);
                if (cfgPaymentNL != null && cfgPaymentNL.VALUE.Contains(siteId.TrimEx()))
                {
                    cart.PaymentNL = BOOLEANTYPE.Yes;
                }
                var conditionResult = _categoryGateway.IsNganLuongPaymentButtonVisible(User.CurrentSite.SITE_ID, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    cart.isVisibleNL = conditionResult;
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                ViewBag.Error = ex.Message;
            }

            return View(cart);
        }

        public JsonResult GenCharge(string taxNo)
        {
            string transId = CommonUtilities.GetTransId();
            List<PaymentChargeDto> paymentCharges = new List<PaymentChargeDto>();
            try
            {
                var siteId = GetSiteId();
                SerilogHelper.WriteTraceLog(
                          siteId,
                          BuildMessageLog(OrderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, taxNo),
                          typeof(CheckoutController),
                          "GenCharge",
                          transId);

                var customerInfo = GetCustomerInfo(siteId, taxNo, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                var billing = _eportBillingGateway.GenBillingChargeByOrderDetail(siteId, ListOrderDetails, taxNo.Trim(),
                    GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                if (billing != null && billing.Charges.HasData())
                {
                    var chargeIds = billing.Charges.Select(x => x.CHARGE_ID).ToList();
                    _eportBillingGateway.ApplyDiscountByPayment(siteId, UserCheckout().USER_ID, chargeIds, GatewayAuthInfor.USER,
                        GatewayAuthInfor.PASSWORD, transId);

                    paymentCharges = _billingGateway.GetPaymentChargesByOrderDetail(siteId, ListOrderDetails, UserCheckout().USER_ID,
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                }

                var paymentTrans = _billingGateway.GetPaymentTransactionByOrderDetail(siteId, ListOrderDetails,
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                var groupFee = paymentCharges.Select(x => new { x.RATE_ID, x.DESCRIPTION }).Distinct();

                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                    customer = customerInfo,
                    charges = paymentCharges,
                    messages = billing?.Messages,
                    transactions = paymentTrans,
                    chargeGroup = groupFee
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        #endregion Page Load

        #region Chi tiết thanh toán

        [HttpGet]
        public virtual JsonResult GetPaidTransaction(string transCode)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {

                var paidCharges = _billingGateway.GetPaidChargesByTransCode(
                    GetSiteId(),
                    transCode,
                    UserCheckout().USER_ID,
                    GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                return JsonAllowGet(paidCharges);
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                return JsonAllowGet(new List<PaymentChargeDto>());
            }
        }

        #endregion Chi tiết thanh toán

        #region In biên nhận

        [HttpPost]
        public JsonResult PreReceipt(List<PaymentTransactionDto> transCodes, int orderId)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                if (transCodes == null || transCodes.Count == 0)
                {
                    throw new ePortException("Chọn ít nhất một dòng dữ liệu để in biên nhận.");
                }

                var notPaidList = new StringBuilder();
                var pid = new StringBuilder();
                var pidStr = string.Empty;
                foreach (var item in transCodes)
                {
                    if (item.PAYMENT_STATUS.ToStringEx() != PAYMENT_STATUS.PAID)
                    {
                        notPaidList.Append(item.TRANSACTION_CODE);
                    }
                    else
                    {
                        pid.Append("," + item.TRANSACTION_CODE);
                    }
                }

                if (!string.IsNullOrWhiteSpace(pid.ToString()))
                {
                    pidStr = pid.ToString().Substring(1);
                }

                if (notPaidList.ToString() != string.Empty)
                {
                    return JsonAllowGet(new
                    {
                        type = MessageType.Error,
                        message = "Chỉ in biên nhận cho giao dịch thanh toán thành công (các giao dịch " + notPaidList + " chưa được thanh toán)."
                    });
                }

                var encrypt = CommonUtilities.StringEncrypt(pidStr);
                var encode = Server.UrlEncode(encrypt);

                BillingSession.Current.BillingHelpSiteId = GetSiteId();
                BillingSession.Current.OrderId = OrderId;

                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                    pid = encode
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        [HttpGet]
        public ActionResult Receipt(string pid)
        {
            string transId = CommonUtilities.GetTransId();

            try
            {
                if (pid.IsEmpty())
                {
                    throw new ePortException("Xảy ra lỗi trong quá trình xử lý!");
                }

                List<string> transactions = CommonUtilities.StringDecrypt(pid).Split(',', ';').ToList();

                InvoiceReport report = new InvoiceReport();
                report.DataSource = GetReceiptData(transactions, transId);
                report.RequestParameters = false;
                for (var i = 0; i < report.Parameters.Count; i++)
                {
                    report.Parameters[i].Visible = false;
                }

                var config = _configGateway.GetCfgSiteParamsByParamName(GetSiteId(), "EPORT", "EPORT", "CompanyName",
                    GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                var logo = _configGateway.GetCfgSiteParamsByParamName(GetSiteId(), "EPORT", "EPORT", "LogoCompany",
                    GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                report.Parameters["ORDER_ID"].Value = BillingSession.Current.OrderId.TrimEx();
                report.Parameters["COMPANY_NAME"].Value = config?.VALUE ?? "";
                if (logo != null && logo.VALUE != null)
                {
                    PrintReportHelper.SetImageXRPictureBox(report, "xrPictureBox1", Server.MapPath(logo.VALUE));
                }
                return View("Receipt", report);
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                ViewBag.Message = ex.Message;
                return View("Receipt");
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                ViewBag.Message = ErrorMessage.HasError;
                return View("Receipt");
            }

        }

        private List<InvoiceDetailModel> GetReceiptData(List<string> transactions, string transId)
        {
            var orderId = BillingSession.Current.OrderId;
            var orderIdTDTC = BillingSession.Current.OrderIdRegisterPortChanges;

            List<ORDER_DETAIL> orderDetails = new List<ORDER_DETAIL>();
            IEnumerable<CHARGE_DTO> charges = new List<CHARGE_DTO>();

            if (orderIdTDTC.Contains(orderId))
            {
                foreach(var odi in orderIdTDTC)
                {
                    var tempOrderdetails = _orderGateway.GetOrderDetailListByOrderID(GetSiteId(), odi, UserCheckout().USER_ID,
                                                                            GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    orderDetails.AddRange(tempOrderdetails);

                    var tempCharges = _billingGateway.GetChargeListByOrderID(GetSiteId(), odi, UserCheckout().USER_ID,
                       GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId).Where(x => !x.REFUND_FLG);
                    charges = charges.Concat(tempCharges);
                }
            }
            else
            {
                orderDetails = _orderGateway.GetOrderDetailListByOrderID(GetSiteId(), orderId, UserCheckout().USER_ID,
                                                                            GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                charges = _billingGateway.GetChargeListByOrderID(GetSiteId(), orderId, UserCheckout().USER_ID,
                   GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId).Where(x => !x.REFUND_FLG);
            }

            if (charges.Count() > 0)
            {
                charges = charges.Where(x => transactions.Contains(x.TRANSACTION_CODE));
            }

            Func<CHARGE_DTO, decimal> getDiscountAmount = (c) =>
            {
                if (CHARGE_CODE.CHARGE_KHTT == c.CHARGE_CODE)
                {
                    return Math.Abs(c.DISCOUNT_AMOUNT);
                }

                DiscountSetting config = JsonConvert.DeserializeObject<DiscountSetting>(c.DISCOUNT_SETTING);

                var amount = (c.AMOUNT_DISCOUNT_BEFORE_TAX * config.DISCNT_LINE
                                            / ((1 + c.TAX_RATE / GlobalSettings.NUMBER_OF_PERCENT * config.CUST_DISCNT_TAX)
                                            + c.AMOUNT_DISCOUNT_PAYMENT_AFTER_TAX * config.DISCNT_PAYMENT_LINE * config.DISCNT_PAYMENT_ON_INV
                                            / ((1 + c.TAX_RATE / GlobalSettings.NUMBER_OF_PERCENT))));

                return (amount * (1 + c.TAX_RATE / 100)).RoundCurrency(0);
            };


            Func<CHARGE_DTO, byte, decimal> getBilledAmount = (c, round) =>
            {
                if (CHARGE_CODE.CHARGE_KHTT == c.CHARGE_CODE)
                {
                    return Math.Abs(c.BILLED_AMOUNT);
                }

                DiscountSetting config = JsonConvert.DeserializeObject<DiscountSetting>(c.DISCOUNT_SETTING);
                var billedAmount = c.AMOUNT_AFTER_DISCOUNT * (1 + c.TAX_RATE / 100);
                var discountAmount = c.AMOUNT_DISCOUNT_BEFORE_TAX * config.DISCNT_LINE / (1 + c.TAX_RATE / 100 * config.CUST_DISCNT_TAX)
                   + c.AMOUNT_DISCOUNT_PAYMENT_AFTER_TAX * config.DISCNT_PAYMENT_LINE * config.DISCNT_PAYMENT_ON_INV / (1 + c.TAX_RATE / 100);
                return (billedAmount - (discountAmount * (1 + c.TAX_RATE / 100))).RoundCurrency(round);
            };

            var chargesDetails = orderDetails.Join(charges, o => o.ORDER_DETAIL_ID, c => c.ORDER_DETAIL_ID, (od, chg) => new
            {
                od.PAYMENT_STATUS,
                od.ORDER_DETAIL_NO,
                od.ITEM_NO,
                od.OPER_METHOD,
                RATE = CHARGE_CODE.CHARGE_KHTT == chg.CHARGE_CODE ? 0 : chg.RATE * chg.CURRENCY_RATE,
                chg.TAX_RATE,
                DISCOUNT_AMOUNT = getDiscountAmount(chg) * chg.CURRENCY_RATE,
                BILLED_AMOUNT = getBilledAmount(chg, 0) * chg.CURRENCY_RATE,
                chg.DESCRIPTION,
                chg.TRANSACTION_CODE,
                chg.BILLED_TO,
                od.CREATED_DATE,
                chg.INVOICE_FKEY,
                SUM_BILLED_AMOUNT = CHARGE_CODE.CHARGE_KHTT == chg.CHARGE_CODE ? chg.BILLED_AMOUNT : getBilledAmount(chg, 10) * chg.CURRENCY_RATE,
                chg.UNITS_CHARGED
            });

            List<InvoiceDetailModel> invoiceDetails = new List<InvoiceDetailModel>();
            var tmpBillTo = string.Empty;
            VE_CUSTOMER customer = null;
            foreach (var detail in chargesDetails)
            {
                var invoiceDetail = new InvoiceDetailModel();
                invoiceDetail.ORDER_DETAIL_NO = detail.ORDER_DETAIL_NO;
                invoiceDetail.ITEM_NO = detail.ITEM_NO;
                invoiceDetail.OPER_METHOD = detail.DESCRIPTION;
                invoiceDetail.BILLED_TO = detail.BILLED_TO;

                invoiceDetail.CREATED_DATE = detail.CREATED_DATE != null ? detail.CREATED_DATE.Value.DateTimeToString() : "";

                if (tmpBillTo != detail.BILLED_TO)
                {
                    tmpBillTo = detail.BILLED_TO;
                    customer = _tOsCategoryGateway.GetCustomerByTaxCode(GetSiteId(), detail.BILLED_TO.TrimEx().ToUpper(),
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId).FirstOrDefault();
                }

                if (customer != null)
                {
                    invoiceDetail.FULL_NAME = customer.FULL_NAME.TrimEx();
                    invoiceDetail.ADDRESS = customer.ADDR.TrimEx();
                }

                var invoiceInfo = _billingGateway.GetInvoiceByFKey(GetSiteId(), detail.INVOICE_FKEY, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                // mau hoa don, ky hieu, so hoa don
                if (invoiceInfo != null)
                {
                    var invoiceCustomers = _eportBillingGateway.SearchInvoicesCustomer(GetSiteId(), invoiceInfo.INVOICE_NO, invoiceInfo.SERIAL,
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (invoiceCustomers != null && invoiceCustomers.Count > 0)
                    {
                        invoiceDetail.PATTERN = invoiceCustomers.FirstOrDefault().PATTERN;
                    }
                    else
                    {
                        invoiceDetail.PATTERN = invoiceInfo.PATTERN;
                    }
                    invoiceDetail.INVOICE_NO = invoiceInfo.INVOICE_NO;
                    invoiceDetail.SERIE = invoiceInfo.SERIAL;
                }
                else
                {
                    if (!string.IsNullOrEmpty(detail.INVOICE_FKEY))
                    {
                        var invoiceDebit = _billingGateway.GetInvoiceByFKey(GetSiteId(), detail.INVOICE_FKEY, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        if (invoiceDebit != null)
                        {
                            var patternId = invoiceDebit.PATTERN_ID.HasValue ? invoiceDebit.PATTERN_ID.Value : 0;
                            invoiceDetail.PATTERN = _authGateway.GetPatternById(GetSiteId(), patternId, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                            invoiceDetail.INVOICE_NO = invoiceDebit.INVOICE_NO;
                            invoiceDetail.SERIE = invoiceDebit.SERIAL;
                        }
                    }
                }

                invoiceDetail.RATE = string.Format(Utils.GetConfigValue("NumberStringFormatting"), detail.RATE);
                invoiceDetail.TAX = string.Format(Utils.GetConfigValue("NumberStringFormatting"), detail.TAX_RATE);
                invoiceDetail.DISCOUNT = string.Format(Utils.GetConfigValue("NumberStringFormatting"), detail.DISCOUNT_AMOUNT);
                invoiceDetail.BILL_AMOUNT = string.Format(Utils.GetConfigValue("NumberStringFormatting"), detail.BILLED_AMOUNT);
                invoiceDetail.TRANSACTION_CODE = detail.TRANSACTION_CODE.TrimEx();
                invoiceDetail.BILLED_AMOUNT = detail.SUM_BILLED_AMOUNT;
                invoiceDetail.UNITS_CHARGED = detail.UNITS_CHARGED;

                var transaction = _billingGateway.GetTransaction(GetSiteId(), detail.TRANSACTION_CODE, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                if (transaction != null && transaction.PAYMENT_METHOD.Equals(PaymentMethod.DEBIT.ToString()))
                {
                    invoiceDetail.BILL_NAME = "GIẤY BIÊN NHẬN ĐĂNG KÝ NỢ";
                }
                else
                {
                    invoiceDetail.BILL_NAME = "GIẤY BIÊN NHẬN THANH TOÁN";
                }

                invoiceDetails.Add(invoiceDetail);
            }

            return invoiceDetails;
        }


        #endregion In biên nhận

        #region Hủy giao dịch

        [HttpPost]
        public JsonResult CancelWaiting(List<PaymentTransactionDto> transCodes, int orderId)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                var transIdError = "";
                var result = false;

                if (transCodes == null || transCodes.Count == 0)
                {
                    return JsonAllowGet(new
                    {
                        type = MessageType.Error,
                        message = "Chọn dòng có chứa mã giao dịch cần hủy"
                    });
                }

                var notWaitingList = new StringBuilder();
                List<OrderDetailDebitDto> dknCompleted = new List<OrderDetailDebitDto>();
                List<string> dknPayed = new List<string>();

                foreach (var item in transCodes)
                {
                    if (item.PAYMENT_STATUS.ToStringEx() != PAYMENT_STATUS.WAITING)
                    {
                        if (item.PAYMENT_STATUS.ToStringEx() == PAYMENT_STATUS.PAID && item.PAYMENT_METHOD == PaymentMethod.DEBIT.ToString())
                        {
                            //Nếu giao dịch có lỗi thì không cần kiểm tra ở dưới
                            if (notWaitingList.Length > 0) continue;

                            //Kiểm tra các phương án đã hoàn tất tác nghiệp làm hàng
                            dknCompleted = _orderGateway.CheckCompletedOrderDetail(GetSiteId(), item.TRANSACTION_CODE,
                                GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                            //Kiểm tra các giao dịch đã tạo ĐNTT/xác nhận/ xuất hóa đơn.
                            dknPayed = _orderGateway.CheckCancelDebit(GetSiteId(), item.TRANSACTION_CODE,
                                GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        }
                        else
                        {
                            notWaitingList.Append(item.TRANSACTION_CODE).Append(",");
                        }
                    }
                }

                var siteId = GetSiteId();
                var cfgPaymentNL = _configGateway.GetePortSetting(siteId, AmEportSettingKey.SiteApplyPaymentNL, GatewayAuthInfor.USER,
                    GatewayAuthInfor.PASSWORD, transId);

                if (notWaitingList.Length > 0)
                {
                    return JsonAllowGet(new
                    {
                        type = MessageType.Error,
                        message = "Chỉ hủy giao dịch cho các giao dịch " +
                        (cfgPaymentNL != null && cfgPaymentNL.VALUE.Contains(siteId.TrimEx()) ? " " : "ATM ") +
                        "ở trạng thái chờ (các giao dịch " +
                        notWaitingList.Remove(notWaitingList.Length - 1, 1) + " không phải trạng thái chờ - W)."
                    });
                }

                if (dknCompleted.Count > 0)
                {
                    string itemNo = string.Join(",", dknCompleted.Where(x => !string.IsNullOrEmpty(x.ITEM_NO)).Select(x => x.ITEM_NO));
                    string.Format(itemNo.Length > 0 ? "thuộc container {0} " : "{0}", itemNo);
                    return JsonAllowGet(new
                    {
                        type = MessageType.Error,
                        message = string.Format("Phí đăng ký nợ không được phép hủy đăng ký nợ hoặc phí đăng ký nợ {0} đã có phiếu Eir nên không được phép hủy đăng ký nợ.", itemNo)
                    });
                }

                if (dknPayed.Count > 0)
                {
                    return JsonAllowGet(new
                    {
                        type = MessageType.Error,
                        message = "Giao dịch: " + string.Join(",", dknPayed) + " đã tạo ĐNTT/xác nhận/xuất hóa đơn không cho phép hủy đăng ký nợ."
                    });
                }

                foreach (var item in transCodes)
                {
                    //Không xóa charge ở topo
                    //if (item.PAYMENT_METHOD == PaymentMethod.DEBIT.ToString())
                    //{
                    //    _orderGateway.CancelDebitOnline(GetSiteId(), item.TRANSACTION_CODE,
                    //        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId)
                    //}

                    //change paymnent status value to W "wating" in orderdetail, charge, transaction table
                    result = _eportBillingGateway.CancelTransactionDebit(GetSiteId(), item.TRANSACTION_CODE.TrimEx(),
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (!result)
                    {
                        transIdError = transId + ",";
                    }
                }

                if (!transIdError.IsEmpty() && !result)
                {
                    return JsonAllowGet(new
                    {
                        type = MessageType.Error,
                        message = "Service hủy giao dịch " + transIdError + " thất bại."
                    });
                }
                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                    message = ""
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        #endregion Hủy giao dịch

        #region Ultility

        private async Task<Message> SendQueryDRToNapas(List<PaymentTransactionDto> transCodes)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (transCodes == null || transCodes.Count == 0)
                {
                    return new Message()
                    {
                        MessageType = MessageType.Error,
                        MessageContent = "Chọn ít nhất một dòng dữ liệu để truy vấn giao dịch."
                    };
                }

                var notWaitingList = new StringBuilder();
                foreach (var item in transCodes)
                {
                    if (item.PAYMENT_STATUS.ToStringEx() != PAYMENT_STATUS.WAITING)
                    {
                        notWaitingList.Append(item.TRANSACTION_CODE).Append(",");
                    }
                }

                var siteId = GetSiteId();
                var cfgPaymentNL = _configGateway.GetePortSetting(siteId, AmEportSettingKey.SiteApplyPaymentNL, GatewayAuthInfor.USER,
                   GatewayAuthInfor.PASSWORD, transId);
                if (notWaitingList.ToString() != string.Empty)
                {
                    return new Message()
                    {
                        MessageType = MessageType.Error,
                        MessageContent = "Chỉ truy vấn cho các giao dịch " +
                         (cfgPaymentNL != null && cfgPaymentNL.VALUE.Contains(siteId.TrimEx()) ? " " : "ATM ")
                        + "ở trạng thái chờ (các giao dịch " +
                        notWaitingList.Remove(notWaitingList.Length - 1, 1) + " không phải trạng thái chờ - W)."
                    };
                }

                var transCode = "";
                //không có giao dịch chờ thanh toán => thoát
                if (transCodes.Count > 0)
                {
                    var transCodeNL = transCodes.Where(o => o.PARTNER_ID == PaymentPartner.NGANLUONG.ToString()).ToList();
                    var transCodeNapas = transCodes.Where(o => o.PARTNER_ID != PaymentPartner.NGANLUONG.ToString()).ToList();

                    //Truy vấn giao dịch NAPAS
                    if (transCodeNapas != null && transCodeNapas.Count > 0)
                    {
                        var configParamList = _configGateway.GetCfgSiteParams(GetSiteId(), FEATURE_CATEGORY.PAYMENT, PaymentPartner.NAPASV3.ToString(),
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                        if (configParamList == null || !configParamList.Any())
                        {
                            throw new ePortException("Không tìm thấy cấu hình thah toán NAPASV3");
                        }

                        var merchant = new MerchantInfo();
                        var token = GetToken(ref merchant, configParamList, transId);

                        if (token == null)
                        {
                            return new Message()
                            {
                                MessageType = MessageType.Error,
                                MessageContent = "Hệ thống không cấp được TOKEN"
                            };
                        }

                        foreach (var item in transCodeNapas)
                        {
                            transCode = item.TRANSACTION_CODE.ToStringEx();

                            merchant.QueryDrUrl = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.QueryDrUrl));
                            merchant.username = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.username));

                            var res = PaymentProcessing.SendQueryDr(merchant, transCode, token.access_token);
                            var data = JsonConvert.SerializeObject(res);

                            var msg = $"UserName: {UserCheckout().USER_ID} \tDirection: QUERYDR_RECEIVE \tMessage: {data}";
                            SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                            if (res.result == BOOLEANTYPE.SUCCESS)
                            {
                                PaymentInfoRequest request = new PaymentInfoRequest
                                {
                                    TransactionCode = transCode,
                                    PaidUser = UserCheckout().USER_ID,
                                    PaidUserName = UserCheckout().USER_NAME,
                                    PaymentRefNo = res.transaction.acquirer.transactionId,
                                    TransactionStatus = PAYMENT_STATUS.PAID,
                                };

                                _eportBillingGateway.UpdatePaymentInfo(GetSiteId(), request, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                            }
                        }
                    }

                    //Truy vấn giao dịch Ngân Lượng
                    if (transCodeNL != null && transCodeNL.Count > 0)
                    {
                        var configParamList = _configGateway.GetCfgSiteParams(GetSiteId(), FEATURE_CATEGORY.PAYMENT, PaymentPartner.NGANLUONG.ToString(),
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                        if (configParamList == null || !configParamList.Any())
                        {
                            throw new ePortException("Không tìm thấy cấu hình thanh toán Ngân Lượng");
                        }

                        string token = await PaymentProcessing.GetTokenAsyncWithNganLuong(configParamList, transId);
                        if (token.IsEmpty())
                        {
                            return new Message()
                            {
                                MessageType = MessageType.Error,
                                MessageContent = "Hệ thống thanh toán không thể cấp token."
                            };
                        }
                        foreach (var item in transCodeNL)
                        {
                            transCode = item.TRANSACTION_CODE.TrimEx();

                            NganLuongPaymentData res = await PaymentProcessing.SendQueryNganLuong(GetSiteId(), transCode, configParamList, token, transId);
                            var data = JsonConvert.SerializeObject(res);

                            var msg = $"UserName: {UserCheckout().USER_ID} \tDirection: SendQueryNganLuong_RECEIVE \tMessage: {data}";
                            SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                            if (res != null && res.TransactionStatus == NganLuongResponseCode.Success)
                            {
                                PaymentInfoRequest request = new PaymentInfoRequest
                                {
                                    TransactionCode = transCode,
                                    PaidUser = UserCheckout().USER_ID,
                                    PaidUserName = UserCheckout().USER_NAME,
                                    PaymentRefNo = res.TransactionId,
                                    TransactionStatus = PAYMENT_STATUS.PAID,
                                    PaymentMethod = res.PaymentMethod
                                };

                                _eportBillingGateway.UpdatePaymentInfo(GetSiteId(), request, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                            }
                        }
                    }
                }

                return new Message()
                {
                    MessageType = MessageType.Success,
                    MessageContent = ""
                };
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                return new Message()
                {
                    MessageType = MessageType.Error,
                    MessageContent = ex.Message
                };
            }
        }

        public JsonResult GetCustomer(string taxNo, int orderId)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                var customerInfo = GetCustomerInfo(GetSiteId(), taxNo, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                    customer = customerInfo,
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        private CustomerDto GetCustomerInfo(string site, string taxFileNo, string wsUsername, string wsPassword, string wsSecureKey)
        {
            string transId = CommonUtilities.GetTransId();

            if (string.IsNullOrWhiteSpace(taxFileNo))
            {
                throw new ePortException(string.Format("Mã số thuế đã nhập không hợp lệ."));
            }

            CustomerDto custResult = new CustomerDto();
            try
            {
                var arrCustomer = _tOsCategoryGateway.GetCustomerByTaxCode(GetSiteId(), taxFileNo,
              GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, wsSecureKey);

                if (arrCustomer == null || arrCustomer.Count == 0)
                {
                    throw new ePortException(string.Format("Không tìm thấy thông tin khách hàng có mã số thuế {0}.", taxFileNo));
                }

                var cust = arrCustomer.SingleOrDefault();

                custResult.TaxNo = cust.TAX_CODE.TrimEx();
                custResult.FullName = cust.FULL_NAME.TrimEx();
                custResult.Address = cust.ADDR.TrimEx();
            }
            catch (Exception ex)
            {
                LogManager.WriteError(ex, wsSecureKey);
                throw new ePortException(string.Format("Không tìm thấy thông tin khách hàng có mã số thuế {0}.", taxFileNo));
            }

            try
            {
                //Tải thông tin khách hàng thân thiết
                var loyalty = _eportBillingGateway.QueryLoyalty(GetSiteId(), custResult.TaxNo,
                    GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                string point = "0";
                bool isKHTT = false;

                if (loyalty != null && !loyalty.ErrorCode.TrimEx().CompareIgnoreCase("ERR_02"))
                {
                    point = loyalty.AvailableDiscount.ToString("#,###,###");
                    isKHTT = loyalty.UseLoyalty;
                }

                custResult.Point = point;
                custResult.IsKhtt = isKHTT;
            }
            catch (Exception ex)
            {
                LogManager.WriteError(ex, wsSecureKey);
            }

            return custResult;
        }

        [HttpPost]
        public async Task<JsonResult> QueryTransaction(List<PaymentTransactionDto> transCodes, int orderId)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                if (transCodes == null || transCodes.Count == 0)
                {
                    throw new ePortException("Chọn ít nhất một dòng dữ liệu để truy vấn giao dịch.");
                }
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }

            var rs = await SendQueryDRToNapas(transCodes);
            return JsonAllowGet(new
            {
                type = rs.MessageType,
                message = rs.MessageContent
            });
        }

        private AM_USER GetEPortUser()
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                var userName = WebUtils.GetValueAppConfigSection("PaymentUser", "AnonymousUser");
                var user = _authGateway.GetUserByUsername(GetSiteId(), userName, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                return new AM_USER
                {
                    USER_ID = user.USER_ID,
                    USER_NAME = user.USER_NAME,
                    POST_PAID_FLG = string.Empty
                };
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                throw new ePortException("Không tìm thấy thông tin User");
            }
        }

        private TokenResponse GetToken(ref MerchantInfo merchant, List<CFG_SITE_PARAMS> configParamList, string transId)
        {
            merchant.grant_type = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.grant_type));
            merchant.username = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.username));
            merchant.password = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.password));
            merchant.client_id = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.client_id));
            merchant.client_secret = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.client_secret));
            merchant.TokenUrl = WebUtils.GetConfigValue(configParamList, nameof(MerchantInfo.TokenUrl));

            var token = PaymentProcessing.GetToken(merchant.TokenUrl, merchant.grant_type, merchant.client_id, merchant.client_secret,
                merchant.username, merchant.password, transId);

            return token;
        }

        private bool Checksum(string data, string secretKey, string checksum)
        {
            var res = WebUtils.GenerateSHA256String(data + secretKey);
            return res == checksum;
        }

        public ActionResult GetAmountTotal(List<int> chargeIds)
        {
            var transId = CommonUtilities.GetTransId();
            try
            {
                if (chargeIds != null && chargeIds.Count > 0)
                {
                    var listCharge = _billingGateway.GetChargeList(GetSiteId(), chargeIds, GatewayAuthInfor.USER,
                        GatewayAuthInfor.PASSWORD, transId);
                    var amountTotal = _eportBillingGateway.GetPaymantAmount(GetSiteId(), listCharge, GatewayAuthInfor.USER,
                        GatewayAuthInfor.PASSWORD, transId);

                    var paidCharges = Mapper.Map<List<PaymentChargeDto>>(listCharge);
                    return JsonAllowGet(new
                    {
                        amountTotal = amountTotal.ToString("##,##"),
                        discountOnOrder = paidCharges.Sum(x => x.DISCOUNT_ON_ORDER).ToString("##,##")
                    });
                }
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
            }
            return Json("", JsonRequestBehavior.AllowGet);
        }

        public ActionResult GetSumCharge(List<int> chargeIds, string taxCode)
        {
            string type = MessageType.Error, message = string.Empty, error = string.Empty;
            var transId = CommonUtilities.GetTransId();

            try
            {
                if (chargeIds != null && chargeIds.Count > 0)
                {
                    var sumCharge = _eportBillingGateway.GetSumCharge(GetSiteId(), taxCode, DateTime.Now,
                      GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    var listCharge = _billingGateway.GetChargeList(GetSiteId(), chargeIds,
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    var amountTotal = _eportBillingGateway.GetPaymantAmount(GetSiteId(), listCharge,
                        GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    var maxAmountATM = AppSettings.GetMaxAmountATM();

                    if (sumCharge + (double)amountTotal > maxAmountATM)
                    {
                        type = MessageType.Success;
                        message = $"Tổng giá trị hóa đơn thuộc Mã số thuế {taxCode} trong ngày {DateTime.Now.ToString(GlobalSettings.StrDMY)}" +
                            $" trên {maxAmountATM.ToString("##,##")} VNĐ. Bạn có muốn thực hiện thanh toán.";
                    }
                }
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                error = ex.Message;
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                error = ErrorMessage.HasError;
            }

            return JsonAllowGet(new
            {
                type,
                message,
                error
            });
        }

        #endregion Ultility

        #region Validate Payment

        private bool ValidatePayment(string taxCode, List<int> chargeIds, string transId)
        {
            try
            {
                if (chargeIds == null || chargeIds.Count <= 0)
                {
                    throw new ePortException("Vui lòng chọn ít nhất 1 dòng phí để thanh toán.");
                }

                // Kiểm tra phí thay đổi tàu cảng đã được xác nhận tại cảng
                var chargeDTXU = _eportBillingGateway.CheckChargeDTXU(GetSiteId(), chargeIds, GatewayAuthInfor.USER,
                  GatewayAuthInfor.PASSWORD, transId);
                if (chargeDTXU.HasData())
                {
                    throw new ePortException(string.Format("Số đăng kí {0} chưa được xác nhận tại cảng.",
                        string.Join(",", chargeDTXU.Select(x => x.ORDER_DETAIL_NO))));
                }

                if (taxCode.IsEmpty())
                {
                    throw new ePortException("Vui lòng nhập mã số thuế.");
                }

                //kiểm tra mã số thuế
                var customer = _tOsCategoryGateway.GetCustomerByTaxCode(GetSiteId(), taxCode.ToUpper(), GatewayAuthInfor.USER,
                  GatewayAuthInfor.PASSWORD, transId);

                if (customer == null || customer.Count < 1)
                {
                    throw new ePortException("Mã số thuế không tồn tại trong hệ thống.");
                }
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                throw ex;
            }
            return true;
        }

        private bool ValidatePaymentByNAPAS(string taxCode, List<int> chargeIds, int discount)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (ValidatePayment(taxCode, chargeIds, transId))
                {
                    if (discount.CheckDecimalEx() <= 0)
                    {
                        return true;
                    }

                    if (discount.CheckDecimalEx() < 1000)
                    {
                        throw new ePortException("Chiết khấu phải là bội số của 1000.");
                    }

                    var paymentCharges = _billingGateway.GetPaymentChargesByOrderDetail(GetSiteId(), ListOrderDetails, UserCheckout().USER_ID,
                            GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    var charges = paymentCharges.Where(a => chargeIds.Contains(a.CHARGE_ID)).ToList();
                    var req = new CheckingLoyaltyRequest();
                    req.AmountBeforeTaxAndDiscount = charges.Sum(c => c.DISPLAY_BILLED_AMOUNT * c.CURRENCY_RATE);
                    req.Discount = discount.CheckDecimalEx();
                    req.TaxRegistrationNo = taxCode;
                    req.Token = User != null ? User?.TaxFileNo : taxCode;

                    var res = _eportBillingGateway.CheckRoyal(GetSiteId(), req, chargeIds, GatewayAuthInfor.USER,
                      GatewayAuthInfor.PASSWORD, transId);

                    if (res == null)
                    {
                        throw new ePortException(string.Format("Mã số thuế {0} không thuộc danh sách khách thân thiết.", taxCode));
                    }

                    if (!res.Valid)
                    {
                        throw new ePortException(res.ErrorCode + ": " + res.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                throw ex;
            }
            return true;
        }

        #endregion Validate Payment

        #region Payment by NAPAS

        public ActionResult PaymentByNAPAS(string taxCode, List<int> chargeIds, int orderId, string pid, int discount = 0, bool verifyMaxAmountAtm = false)
        {
            string transId = CommonUtilities.GetTransId();
            try
            {
                var configUseLoyalty = _configGateway.GetePortSetting(GetSiteId(), AmEportSettingKey.UseLoyalty, GatewayAuthInfor.USER,
                    GatewayAuthInfor.PASSWORD, transId);

                if (configUseLoyalty != null && configUseLoyalty.VALUE.Equals("N") && discount > 0)
                {
                    throw new ePortException("Hệ thống sử dụng điểm khách hàng thân thiết đang bảo trì.");
                }

                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                List<ORDER_DETAIL> ods = null;
                if (!ValidateOrderId(GetSiteId(), pid, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId, ref ods))
                {
                    SerilogHelper.WriteBugLogic(GetSiteId(), $"ValidateOrderId - pid : {pid} - ORDER_DETAIL: {ods?.ToJson()}", typeof(CheckoutController), "PaymentByNAPAS", transId);
                    throw new ePortException("Xảy ra lỗi trong quá trình thanh toán, vui lòng thử lại.");
                }

                var checkValid = ValidateCharge(GetSiteId(), orderId, chargeIds, ods, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                if (!checkValid.IsEmpty())
                {
                    throw new ePortException(checkValid);
                }

                SerilogHelper.WriteTraceLog(
                       GetSiteId(),
                       BuildMessageLog(orderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, taxCode, chargeIds),
                       typeof(CheckoutController),
                       "PaymentByNAPAS",
                       transId);

                if (ValidatePaymentByNAPAS(taxCode, chargeIds, discount))
                {
                    //Chiết khấu khách hàng thân thiết
                    if (discount.CheckDecimalEx() > 0)
                    {
                        var loyalty = _eportBillingGateway.GenChargeLoyalty(GetSiteId(), chargeIds,
                            discount, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                        chargeIds.AddRange(loyalty);
                    }

                    var transaction = _eportBillingGateway.CreateTransactionByChargeId(GetSiteId(),
                        chargeIds, UserCheckout().USER_ID, PaymentPartner.NAPASV3.ToString(), PaymentMethod.DOMESTIC_CARD.ToString(),
                        taxCode, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (transaction == null)
                    {
                        throw new ePortException("Không thể tạo mã giao dịch");
                    }

                    if (transaction.BILLED_AMOUNT.Value == 0)
                    {
                        PaymentInfoRequest request = new PaymentInfoRequest
                        {
                            TransactionCode = transaction.TRANSACTION_CODE,
                            PaidUser = UserCheckout().USER_ID,
                            PaidUserName = UserCheckout().USER_NAME,
                            PaymentRefNo = "FREE",
                            TransactionStatus = PAYMENT_STATUS.PAID,
                        };

                        _eportBillingGateway.UpdatePaymentInfo(GetSiteId(), request, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        return JsonAllowGet(new
                        {
                            type = MessageType.FreeSuccess,
                            message = "Thanh toán thành công"
                        });
                    }

                    //->Tính tổng tiền
                    var sumCharge = _eportBillingGateway.GetSumCharge(GetSiteId(), taxCode, DateTime.Now,
                      GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    var maxAmountATM = AppSettings.GetMaxAmountATM();
                    var sumAmout = double.Parse(transaction.BILLED_AMOUNT.HasValue ? transaction.BILLED_AMOUNT.Value.ToString() : "0");

                    if (verifyMaxAmountAtm && (sumCharge + sumAmout) > maxAmountATM)
                    {
                        BillingSession.Current.UpdateTransactionRemark = $"{GetSiteId()}__//{transaction.TRANSACTION_CODE}__//" +
                            $"Khách hàng xác nhận Xuất HĐ lớn hơn {maxAmountATM} triệu.";
                    }
                    else
                    {
                        BillingSession.Current.UpdateTransactionRemark = null;
                    }

                    //Tạo application key theo transactionid để cập nhật t.toán
                    if (Session[transaction.TRANSACTION_CODE] == null)
                    {
                        Session[transaction.TRANSACTION_CODE] = transaction.TRANSACTION_CODE;
                    }

                    //Sử dụng tiền trong KHTT
                    if (discount > 0)
                    {
                        //Trừ tiền vào ví tiền KHTT
                        var result = _eportBillingGateway.UseLoyaltyDiscount(GetSiteId(), transaction.TRANSACTION_CODE,
                            LoyaltyActivity.THEM.ToString(), GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        if (!result)
                        {
                            throw new ePortException(string.Format("Sử dụng tiền chiết khấu KHTT của MST: {0} không thành công.", taxCode));
                        }
                    }

                    var configParamList = _configGateway.GetCfgSiteParams(GetSiteId(), FEATURE_CATEGORY.PAYMENT, PaymentPartner.NAPASV3.ToString(),
                            GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (configParamList == null || !configParamList.Any())
                    {
                        throw new ePortException("Không tìm thấy cấu hình thah toán NAPASV3");
                    }

                    //Napas V3.0
                    MerchantInfo merchant = new MerchantInfo();
                    TokenResponse token = GetToken(ref merchant, configParamList, transId);
                    if (token == null)
                    {
                        throw new ePortException("Hệ thống thanh toán không thể cấp token.");
                    }

                    //get datakey
                    var dataKey = PaymentProcessing.GetDataKey(configParamList, token.access_token, transaction);
                    if (dataKey == null)
                    {
                        throw new ePortException("Hệ thống thanh toán không thể cấp data key.");
                    }

                    merchant.orderId = transaction.TRANSACTION_CODE;
                    merchant.orderAmount = transaction.BILLED_AMOUNT.Value.ToStringEx();
                    merchant.orderReference = _orderGateway.GetOrder(GetSiteId(), orderId, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD,
                        transId)?.ORDER_ID.ToStringEx();

                    //Setup NAPAS
                    merchant = SetupMerchantInfo(merchant, token, dataKey, configParamList);

                    var data = JsonConvert.SerializeObject(merchant);
                    //ghi log gửi dữ liệu sang Napas
                    WebUtils.WriteLogOutSite(UserCheckout().USER_NAME, "SEND", data);

                    var dataMc = JsonConvert.SerializeObject(merchant).HideSensitiveData();
                    var msg = $"UserName: {UserCheckout().USER_NAME} \tDirection: SEND \tMessage: {dataMc}";

                    SerilogHelper.WriteTraceLog(
                        GetSiteId(),
                        BuildMessageLog(orderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, msg),
                        typeof(CheckoutController),
                        "PaymentByNAPAS",
                        transId);

                    BillingSession.Current.MerchantInfo = merchant;
                }

                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                    callback = "/Checkout/Payment"
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        private MerchantInfo SetupMerchantInfo(MerchantInfo merchant, TokenResponse token, DataKeyResponse dataKey,
            List<CFG_SITE_PARAMS> ConfigParamList)
        {
            merchant.token = token.access_token;
            merchant.datakey = dataKey.dataKey;
            merchant.napaskey = dataKey.napasKey;
            merchant.action = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.action));
            merchant.merchantId = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.merchantId));
            merchant.clientIP = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.clientIP));
            merchant.deviceId = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.deviceId));
            merchant.environment = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.environment));
            merchant.cardScheme = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.cardScheme));
            merchant.enable3DSecure = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.enable3DSecure));
            merchant.apiOperation = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.apiOperation));
            merchant.orderCurrency = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.orderCurrency));
            merchant.channel = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.channel));
            merchant.sourceOfFundsType = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.sourceOfFundsType));
            merchant.HostedForm = WebUtils.GetConfigValue(ConfigParamList, nameof(MerchantInfo.HostedForm));
            return merchant;
        }

        #endregion Payment by NAPAS

        #region Payment Callback

        public ActionResult Payment()
        {
            if (MerchantInfo != null)
            {
                return View(BillingSession.Current.MerchantInfo);
            }
            TempData["Message"] = "Hệ thống thanh toán không tìm thấy.";
            return RedirectToAction("Index");
        }

        public ActionResult PaymentCallback()
        {
            var keys = Request.Form.AllKeys;
            string message = string.Empty;
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (MerchantInfo == null)
                {
                    throw new ePortException("Hệ thống thanh toán không tìm thấy.");
                }

                NapasHashResult napasHashResult = null;

                for (var i = 0; i < keys.Length; i++)
                {
                    if (keys[i] == nameof(NapasResult.napasResult))
                    {
                        napasHashResult = JsonConvert.DeserializeObject<NapasHashResult>(Request.Form[keys[i]]);
                    }
                }

                if (napasHashResult == null)
                {
                    throw new ePortException("Không ghi nhận được kết quả thanh toán.");
                }

                var userId = UserCheckout().USER_ID.ToString();
                //ghi log dữ liệu từ Napas post sang
                WebUtils.WriteLogOutSite(userId, " RECIVE: " + Request.Url.AbsoluteUri, JsonConvert.SerializeObject(napasHashResult));

                var dataRecive = JsonConvert.SerializeObject(napasHashResult).HideSensitiveData();
                var msg = $"UserName: {userId} \tDirection: RECIVE: {Request.Url.AbsoluteUri} \tMessage: {dataRecive}";

                SerilogHelper.WriteTraceLog(
                      GetSiteId(),
                      BuildMessageLog(OrderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, msg),
                      typeof(CheckoutController),
                      "PaymentCallback",
                      transId);

                if (Checksum(napasHashResult.data, MerchantInfo.client_secret, napasHashResult.checksum))
                {
                    var data = WebUtils.Base64Decode(napasHashResult.data);
                    var napasResult = JsonConvert.DeserializeObject<NapasResult>(data);

                    //ghi log data au khi decode
                    var dataReciveCs = JsonConvert.SerializeObject(data).HideSensitiveData();
                    var msgCs = $"UserName: {userId} \tDirection : RECIVE Checksum: {Request.Url.AbsoluteUri} \tMessage: {dataReciveCs}";
                    SerilogHelper.WriteTraceLog(GetSiteId(), msgCs, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                    if (napasResult.paymentResult.result != "SUCCESS")
                    {
                        message = "Thanh toán không thành công.";
                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    var transCode = napasResult.paymentResult.order.id;
                    var vpc_TransactionNo = napasResult.paymentResult.transaction.acquirer.transactionId;

                    if (!string.IsNullOrEmpty(BillingSession.Current.UpdateTransactionRemark))
                    {
                        var transInfo = BillingSession.Current.UpdateTransactionRemark.ToStringEx();
                        var transInfoArr = transInfo.Split(new[] { "__//" }, StringSplitOptions.RemoveEmptyEntries);

                        _eportBillingGateway.UpdateTransRemark(transInfoArr[0], transInfoArr[1], transInfoArr[2], GatewayAuthInfor.USER,
                          GatewayAuthInfor.PASSWORD, transId);
                        BillingSession.Current.UpdateTransactionRemark = null;
                    }

                    //Chặn cập nhật thanh toán và xuất HD 2 lần
                    if (Session[transCode] == null)
                    {
                        message = "Mã giao dịch: " + transCode + " đã cập nhật thanh toán và xuất HD";
                        WebUtils.WriteLogOutSite("NAPAS", "RECEICE", message);
                        msg = $"UserName: NAPAS \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    //Chỉ check order theo yêu cầu nâng cấp v3.2
                    if (napasResult.paymentResult.order == null)
                    {
                        message = "Không tìm thấy thông tin đơn hàng của giao dịch: " + transCode;
                        WebUtils.WriteLogOutSite("NAPAS", "RECEICE", message);
                        msg = $"UserName: NAPAS \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    var transDto = _billingGateway.GetTransaction(GetSiteId(), transCode, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    if (transDto == null)
                    {
                        message = "Không tìm thấy mã giao dịch: " + transCode;
                        WebUtils.WriteLogOutSite("NAPAS", "RECEICE", message);
                        msg = $"UserName: NAPAS \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    if (transDto.BILLED_AMOUNT.Value.CheckDecimalEx() != napasResult.paymentResult.order.amount.CheckDecimalEx())
                    {
                        message = "Số tiền thanh toán của giao dịch: " + transCode + " không khớp";
                        WebUtils.WriteLogOutSite("NAPAS", "RECEICE", message);
                        msg = $"UserName: NAPAS \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    //xóa transCode ra khỏi Application
                    Session[transCode] = null;

                    PaymentInfoRequest request = new PaymentInfoRequest
                    {
                        TransactionCode = transCode,
                        PaidUser = UserCheckout().USER_ID,
                        PaidUserName = UserCheckout().USER_NAME,
                        PaymentRefNo = vpc_TransactionNo,
                        TransactionStatus = PAYMENT_STATUS.PAID,
                    };

                    _eportBillingGateway.UpdatePaymentInfo(GetSiteId(), request, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                }
                else
                {
                    throw new ePortException("Dữ liệu nhận từ hệ thống thanh toán NAPAS không hợp lệ.");
                }
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                message = ex.Message;
            }

            return RedirectToAction("Index");
        }

        #endregion Payment Callback

        #region Thanh toán qua tài khoản danh nghiệp

        public ActionResult PaymentLater(string taxCode, List<int> chargeIds, int orderId, string pid, int discount = 0)
        {
            string error = string.Empty;

            string transId = CommonUtilities.GetTransId();
            try
            {
                var configUseLoyalty = _configGateway.GetePortSetting(GetSiteId(), AmEportSettingKey.UseLoyalty, GatewayAuthInfor.USER,
                    GatewayAuthInfor.PASSWORD, transId);
                if (configUseLoyalty != null && configUseLoyalty.VALUE.Equals("N") && discount > 0)
                {
                    throw new ePortException("Hệ thống sử dụng điểm khách hàng thân thiết đang bảo trì.");
                }

                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                List<ORDER_DETAIL> ods = null;
                if (!ValidateOrderId(GetSiteId(), pid, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId, ref ods))
                {
                    SerilogHelper.WriteBugLogic(GetSiteId(), $"ValidateOrderId - pid : {pid} - ORDER_DETAIL: {ods?.ToJson()}", typeof(CheckoutController), "PaymentLater", transId);
                    throw new ePortException("Xảy ra lỗi trong quá trình thanh toán, vui lòng thử lại.");
                }

                SerilogHelper.WriteTraceLog(
                  GetSiteId(),
                  BuildMessageLog(orderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, taxCode, chargeIds),
                  typeof(CheckoutController),
                  "PaymentLater",
                  transId);

                var checkValid = ValidateCharge(GetSiteId(), orderId, chargeIds, ods, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                if (!checkValid.IsEmpty())
                {
                    throw new ePortException(checkValid);
                }

                if (ValidatePaymentByNAPAS(taxCode, chargeIds, discount))
                {
                    //Chiết khấu khách hàng thân thiết
                    if (discount.CheckDecimalEx() > 0)
                    {
                        var loyalty = _eportBillingGateway.GenChargeLoyalty(GetSiteId(), chargeIds,
                            discount, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                        chargeIds.AddRange(loyalty);
                    }

                    var transaction = _eportBillingGateway.CreateTransactionByChargeId(GetSiteId(),
                        chargeIds, UserCheckout().USER_ID, PaymentPartner.NAPASV3.ToString(), PaymentMethod.PAYLATER.ToString(),
                        taxCode, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (transaction == null)
                    {
                        throw new ePortException("Không thể tạo mã giao dịch");
                    }

                    //Sử dụng tiền trong KHTT
                    if (discount > 0)
                    {
                        //Trừ tiền vào ví tiền KHTT
                        var result = _eportBillingGateway.UseLoyaltyDiscount(GetSiteId(), transaction.TRANSACTION_CODE,
                            LoyaltyActivity.THEM.ToString(), GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        if (!result)
                        {
                            throw new ePortException(string.Format("Sử dụng tiền chiết khấu KHTT của MST: {0} không thành công.", taxCode));
                        }
                    }

                    #region SEND SMS TO CUSTOMER

                    //Send SMS to customer
                    var order = _orderGateway.GetOrder(GetSiteId(), orderId.CheckIntEx(), GatewayAuthInfor.USER,
                      GatewayAuthInfor.PASSWORD, transId);

                    var user = _authGateway.GetUser(GetSiteId(), order.ORDER_BY.CheckIntEx(), GatewayAuthInfor.USER,
                      GatewayAuthInfor.PASSWORD, transId);

                    var phoneNo = user.PHONE_NO.TrimEx();

                    //(TB) Khach hang {0} da thuc hien thanh toan voi ma giao dich {1},ma lo {2},tong tien {3}.Cam on quy Khach hang su dung dich vu cua chung toi.
                    var smsContent = Utils.GetConfigValue("SMSPATTERN");

                    //https://eport.saigonnewport.com.vn/Pages/Registration/HoaDon?OrderId=20554&vpc_AcqResponseCode=3&vpc_AdditionalData=970403&vpc_Amount=24940000&vpc_CardType=183&vpc_Command=pay&vpc_CurrencyCode=VND&vpc_Locale=vn&vpc_MerchTxnRef=GL87VKJSBC&vpc_Merchant=SNP&vpc_OrderInfo=20554&vpc_ResponseCode=0&vpc_TransactionNo=841113991&vpc_Version=1.1&vpc_SecureHash=B7A182F207840155D812701FDBFD0E11
                    if (!phoneNo.IsEmpty())
                    {
                        smsContent = string.Format(smsContent, user.USER_NAME, transaction.TRANSACTION_CODE, orderId, transaction.BILLED_AMOUNT.Value.ToString("###,###,###"));
                        var errorMesage = "";
                        //var userSms = Utils.GetConfigValue("SMSUSER");
                        //var passSms = Utils.GetConfigValue("SMSPASS");
                        //// to do
                        var flag = _smsGateway.SendSms(GetSiteId(), phoneNo, smsContent, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        if (!flag)
                        {
                            WebUtils.WriteLog("CustomerPages_BillInfor.btnChuyenKhoan_Click:" + errorMesage);
                        }
                    }

                    #endregion SEND SMS TO CUSTOMER
                }

                return JsonAllowGet(new
                {
                    type = MessageType.Success
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        #endregion Thanh toán qua tài khoản danh nghiệp

        #region Đăng ký nợ

        public ActionResult PaymentDebit(string taxCode, List<int> chargeIds, int orderId, string pid, int discount = 0)
        {
            string error = string.Empty;
            var transId = CommonUtilities.GetTransId();

            try
            {
                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }
                List<ORDER_DETAIL> ods = null;
                if (!ValidateOrderId(GetSiteId(), pid, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId, ref ods))
                {
                    SerilogHelper.WriteBugLogic(GetSiteId(), $"ValidateOrderId - pid : {pid} - ORDER_DETAIL: {ods?.ToJson()}", typeof(CheckoutController), "PaymentDebit", transId);
                    throw new ePortException("Xảy ra lỗi trong quá trình thanh toán, vui lòng thử lại.");
                }

                SerilogHelper.WriteTraceLog(
                      GetSiteId(),
                      BuildMessageLog(orderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, taxCode, chargeIds),
                      typeof(CheckoutController),
                      "PaymentDebit",
                      transId);

                var checkValid = ValidateCharge(GetSiteId(), orderId, chargeIds, ods, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                if (!checkValid.IsEmpty())
                {
                    throw new ePortException(checkValid);
                }

                if (ValidatePayment(taxCode, chargeIds, transId))
                {
                    if (User == null || (User != null && !CommonWeb.CheckPostPaid(User)))
                    {
                        throw new ePortException("Tài khoản của bạn không cho phép đăng ký nợ.");
                    }

                    if (discount.CheckDecimalEx() > 0)
                    {
                        throw new ePortException("Thanh toán bằng phương thức Đăng ký nợ không được áp dụng chương trình Khách hàng thân thiết.");
                    }

                    var transaction = _eportBillingGateway.CreateTransactionByChargeId(GetSiteId(),
                        chargeIds, UserCheckout().USER_ID, string.Empty, PaymentMethod.DEBIT.ToString(),
                        taxCode, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (transaction == null)
                    {
                        throw new ePortException("Không thể tạo mã giao dịch");
                    }

                    PaymentInfoRequest request = new PaymentInfoRequest
                    {
                        TransactionCode = transaction.TRANSACTION_CODE.TrimEx(),
                        PaidUser = UserCheckout().USER_ID,
                        PaidUserName = UserCheckout().USER_NAME,
                        //PaymentRefNo = vpc_TransactionNo,
                        TransactionStatus = PAYMENT_STATUS.PAID,
                    };

                    _eportBillingGateway.UpdatePaymentInfo(GetSiteId(), request, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                }

                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }
        #endregion Đăng ký nợ

        private bool ValidateOrderId(string siteId, string pid, string wsUsername, string wsPassword, string wsSecureKey, ref List<ORDER_DETAIL> ods)
        {
            if (pid.IsEmpty())
            {
                return true;
            }

            if (!CommonUtilities.StringEncryptFixedLenght(OrderId.ToString() + string.Join(",", ListOrderDetails) + User?.UserId).Equals(pid))
            {
                return false;
            }

            var orderDetails = _orderGateway.GetOrderDetailListByOrderId(siteId, OrderId, wsUsername, wsPassword, wsSecureKey);

            if (!orderDetails.Any(x => ListOrderDetails.Contains(x.ORDER_DETAIL_ID)))
            {
                return false;
            }

            ods = orderDetails;

            return true;
        }

        private string ValidateCharge(string siteId, int orderId, List<int> chargeIds, List<ORDER_DETAIL> ods, string wsUsername, string wsPassword, string wsSecureKey)
        {
            if (chargeIds == null || chargeIds.Count <= 0)
            {
                return "Vui lòng chọn ít nhất 1 dòng phí để thanh toán.";
            }

            var charges = _billingGateway.GetChargeListByOrderID(siteId, orderId, UserCheckout().USER_ID,
                wsUsername, wsPassword, wsSecureKey);

            var validCharge = charges.Where(x => chargeIds.Contains(x.CHARGE_ID));

            if (!validCharge.HasData())
            {
                return $"Các phí đã chọn không thuộc lô hàng {orderId}";
            }

            bool isZeroBilledAmountCharge = validCharge.Any(x => x.BILLED_AMOUNT <= 0);
            var sumBilledAmount = validCharge.Sum(x => x.BILLED_AMOUNT);

            if(isZeroBilledAmountCharge && sumBilledAmount > 0)
            {
                return $"Vui lòng không chọn phí 0đ cùng với các phí khác!";
            }

            return string.Empty;



        }

        #region Payment Ngân lượng
        public async Task<ActionResult> PaymentByNganLuong(string taxCode, List<int> chargeIds, int orderId, string pid, int discount = 0, bool verifyMaxAmountAtm = false)
        {
            string transId = CommonUtilities.GetTransId();
            string siteId = GetSiteId();
            try
            {
                //Link payment của Ngân Lượng =>Payment service trả về
                string paymentURL = string.Empty;
                var configUseLoyalty = _configGateway.GetePortSetting(siteId, AmEportSettingKey.UseLoyalty, GatewayAuthInfor.USER,
                    GatewayAuthInfor.PASSWORD, transId);
                if (configUseLoyalty != null && configUseLoyalty.VALUE.Equals("N") && discount > 0)
                {
                    throw new ePortException("Hệ thống sử dụng điểm khách hàng thân thiết đang bảo trì.");
                }

                if (!BillingSession.Current.OrderId.Equals(orderId))
                {
                    throw new ePortException("Phiên làm việc này đã hết hạn.");
                }

                List<ORDER_DETAIL> ods = null;
                if (!ValidateOrderId(siteId, pid, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId, ref ods))
                {
                    SerilogHelper.WriteBugLogic(siteId, $"ValidateOrderId - pid : {pid} - ORDER_DETAIL: {ods?.ToJson()}",
                                                    typeof(CheckoutController), nameof(PaymentByNganLuong), transId);
                    throw new ePortException("Xảy ra lỗi trong quá trình thanh toán, vui lòng thử lại.");
                }

                var checkValid = ValidateCharge(siteId, orderId, chargeIds, ods, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                if (!checkValid.IsEmpty())
                {
                    throw new ePortException(checkValid);
                }

                SerilogHelper.WriteTraceLog(
                       siteId,
                       BuildMessageLog(orderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, taxCode, chargeIds),
                       typeof(CheckoutController),
                       nameof(PaymentByNganLuong),
                       transId);

                if (ValidatePaymentByNAPAS(taxCode, chargeIds, discount))
                {
                    //Chiết khấu khách hàng thân thiết
                    if (discount.CheckDecimalEx() > 0)
                    {
                        var loyalty = _eportBillingGateway.GenChargeLoyalty(siteId, chargeIds,
                            discount, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                        chargeIds.AddRange(loyalty);
                    }

                    var transaction = _eportBillingGateway.CreateTransactionByChargeId(siteId,
                        chargeIds, UserCheckout().USER_ID, PaymentPartner.NGANLUONG.ToString(), PaymentMethod.NGANLUONG.ToString(),
                        taxCode, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    if (transaction == null)
                    {
                        throw new ePortException("Không thể tạo mã giao dịch");
                    }

                    //->Tính tổng tiền
                    var sumCharge = _eportBillingGateway.GetSumCharge(siteId, taxCode, DateTime.Now,
                      GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);

                    var maxAmountATM = AppSettings.GetMaxAmountATM();
                    var sumAmout = double.Parse(transaction.BILLED_AMOUNT.HasValue ? transaction.BILLED_AMOUNT.Value.ToString() : "0");

                    if (verifyMaxAmountAtm && (sumCharge + sumAmout) > maxAmountATM)
                    {
                        BillingSession.Current.UpdateTransactionRemark = $"{siteId}__//{transaction.TRANSACTION_CODE}__//" +
                            $"Khách hàng xác nhận Xuất HĐ lớn hơn {maxAmountATM} triệu.";
                    }
                    else
                    {
                        BillingSession.Current.UpdateTransactionRemark = null;
                    }

                    //Tạo application key theo transactionid để cập nhật t.toán
                    if (Session[transaction.TRANSACTION_CODE] == null)
                    {
                        Session[transaction.TRANSACTION_CODE] = transaction.TRANSACTION_CODE;
                    }

                    //Sử dụng tiền trong KHTT
                    if (discount > 0)
                    {
                        //Trừ tiền vào ví tiền KHTT
                        var result = _eportBillingGateway.UseLoyaltyDiscount(siteId, transaction.TRANSACTION_CODE,
                            LoyaltyActivity.THEM.ToString(), GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                        if (!result)
                        {
                            throw new ePortException(string.Format("Sử dụng tiền chiết khấu KHTT của MST: {0} không thành công.", taxCode));
                        }
                    }

                    var configParamList = _configGateway.GetCfgSiteParams(siteId, FEATURE_CATEGORY.PAYMENT, PaymentPartner.NGANLUONG.ToString(),
                            GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    if (configParamList == null || !configParamList.Any())
                    {
                        throw new ePortException("Không tìm thấy cấu hình thah toán Ngân Lượng");
                    }

                    //Ngân lượng
                    NganLuongMerchantInfo merchant = new NganLuongMerchantInfo();
                    merchant.TokenUrl = WebUtils.GetConfigValue(configParamList, nameof(NganLuongMerchantInfo.TokenUrl));
                    merchant.ClientId = WebUtils.GetConfigValue(configParamList, nameof(NganLuongMerchantInfo.ClientId));
                    merchant.ClientSecret = WebUtils.GetConfigValue(configParamList, nameof(NganLuongMerchantInfo.ClientSecret));
                    string token = await PaymentProcessing.GetTokenAsyncWithNganLuong(configParamList, transId);
                    //string token = GetTokenNganLuong(configParamList, transId);
                    if (token.IsEmpty())
                    {
                        throw new ePortException("Hệ thống thanh toán không thể cấp token.");
                    }
                    var userCheckout = UserCheckout();

                    //Setup data Ngân Lượng
                    merchant.OrderId = transaction.TRANSACTION_CODE;
                    merchant.Amount = transaction.BILLED_AMOUNT.Value.ToStringEx();
                    merchant.Description = orderId.ToString();
                    merchant.OrderReference = _orderGateway.GetOrder(siteId, orderId, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD,
                        transId)?.ORDER_ID.ToStringEx();


                    var data = JsonConvert.SerializeObject(merchant);
                    //ghi log gửi dữ liệu sang Napas
                    WebUtils.WriteLogOutSite(userCheckout.USER_NAME, "SEND", data);

                    var dataMc = JsonConvert.SerializeObject(merchant).HideSensitiveData();
                    var msg = $"UserName: {userCheckout.USER_NAME} \tDirection: SEND \tMessage: {dataMc}";

                    paymentURL = await PaymentProcessing.RequestPaymentNganLuongAsync(siteId, token, configParamList, merchant, transId);

                    SerilogHelper.WriteTraceLog(
                        siteId,
                        BuildMessageLog(orderId, ListOrderDetails, userCheckout.USER_ID, HttpContext.Session?.SessionID, paymentURL),
                        typeof(CheckoutController),
                        "PaymentByNganLuong",
                        transId);

                    if (paymentURL.IsEmpty())
                    {
                        return JsonAllowGet(new
                        {
                            type = MessageType.Error,
                            message = "Hệ thống thanh toán không thể cấp link kết nối tới Ngân Lượng."
                        });
                    }

                    BillingSession.Current.NganLuongMerchantInfo = merchant;
                }
                return JsonAllowGet(new
                {
                    type = MessageType.Success,
                    data = paymentURL
                });
            }
            catch (ePortException ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);

                return JsonAllowGet(new
                {
                    type = MessageType.Error,
                    message = ErrorMessage.HasError
                });
            }
        }

        public async Task<ActionResult> PaymentByNganLuongCallback()
        {
            var keys = Request.Params.AllKeys;
            string message = string.Empty;
            string transId = CommonUtilities.GetTransId();
            try
            {
                if (NganLuongMerchantInfo == null)
                {
                    throw new ePortException("Hệ thống thanh toán không tìm thấy.");
                }

                if (keys == null || keys.Length == 0 || Request.Params["order_code"].IsEmpty())
                {
                    throw new ePortException("Không ghi nhận được kết quả thanh toán.");
                }

                NganLuongResult nganLuongResult = new NganLuongResult()
                {
                    TransactionCode = Request.Params["order_code"],
                    Price = Request.Params["price"],
                    PaymentRefNo = Request.Params["payment_id"],
                    PaymentType = Request.Params["payment_type"],
                    ErrorText = Request.Params["error_text"],
                    SecureCode = Request.Params["secure_code"],
                    TokenNL = Request.Params["token_nl"],
                };

                var userId = UserCheckout().USER_ID.ToString();
                //ghi log dữ liệu từ Ngân Lượng post sang
                WebUtils.WriteLogOutSite(userId, " RECIVE: " + Request.Url.AbsoluteUri, JsonConvert.SerializeObject(nganLuongResult));

                var dataRecive = JsonConvert.SerializeObject(nganLuongResult).HideSensitiveData();
                var msg = $"UserName: {userId} \tDirection: RECIVE: {Request.Url.AbsoluteUri} \tMessage: {dataRecive}";

                SerilogHelper.WriteTraceLog(
                      GetSiteId(),
                      BuildMessageLog(OrderId, ListOrderDetails, UserCheckout().USER_ID, HttpContext.Session?.SessionID, msg),
                      typeof(CheckoutController),
                      "PaymentByNganLuongCallback",
                      transId);

                if (!nganLuongResult.ErrorText.IsEmpty())
                {
                    message = "Thanh toán không thành công.";
                    TempData["Message"] = message;
                    return RedirectToAction("Index");
                }

                var configParamList = _configGateway.GetCfgSiteParams(GetSiteId(), FEATURE_CATEGORY.PAYMENT, PaymentPartner.NGANLUONG.ToString(),
                           GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                if (configParamList == null || !configParamList.Any())
                {
                    throw new ePortException("Không tìm thấy cấu hình thah toán Ngân Lượng");
                }

                string token = await PaymentProcessing.GetTokenAsyncWithNganLuong(configParamList, transId);
                if (token.IsEmpty())
                {
                    message = "Hệ thống thanh toán không thể cấp token.";
                    TempData["Message"] = message;
                    return RedirectToAction("Index");
                }

                NganLuongPaymentData res = await PaymentProcessing.SendQueryNganLuong(GetSiteId(), nganLuongResult.TransactionCode, configParamList, token, transId);
                var data = JsonConvert.SerializeObject(res);
                msg = $"UserName: {UserCheckout().USER_ID} \tDirection: SendQueryNganLuong_RECEIVE \tMessage: {data}";
                SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                if (res != null && res.TransactionStatus == NganLuongResponseCode.Success)
                {
                    var transCode = nganLuongResult.TransactionCode;
                    var vpc_TransactionNo = nganLuongResult.PaymentRefNo;

                    if (!string.IsNullOrEmpty(BillingSession.Current.UpdateTransactionRemark))
                    {
                        var transInfo = BillingSession.Current.UpdateTransactionRemark.ToStringEx();
                        var transInfoArr = transInfo.Split(new[] { "__//" }, StringSplitOptions.RemoveEmptyEntries);

                        _eportBillingGateway.UpdateTransRemark(transInfoArr[0], transInfoArr[1], transInfoArr[2], GatewayAuthInfor.USER,
                          GatewayAuthInfor.PASSWORD, transId);
                        BillingSession.Current.UpdateTransactionRemark = null;
                    }

                    //Chặn cập nhật thanh toán và xuất HD 2 lần
                    if (Session[transCode] == null)
                    {
                        message = "Mã giao dịch: " + transCode + " đã cập nhật thanh toán và xuất HD";
                        WebUtils.WriteLogOutSite("NGANLUONG", "RECEICE", message);
                        msg = $"UserName: NGANLUONG \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }
                    //NapasResult napasResult = new NapasResult();
                    ////Chỉ check order theo yêu cầu nâng cấp v3.2
                    //if (napasResult.paymentResult.order == null)
                    //{
                    //    message = "Không tìm thấy thông tin đơn hàng của giao dịch: " + transCode;
                    //    WebUtils.WriteLogOutSite("NAPAS", "RECEICE", message);
                    //    msg = $"UserName: NAPAS \tDirection: RECEICE \tMessage: {message}";
                    //    SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);

                    //    TempData["Message"] = message;
                    //    return RedirectToAction("Index");
                    //}

                    var transDto = _billingGateway.GetTransaction(GetSiteId(), transCode, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                    if (transDto == null)
                    {
                        message = "Không tìm thấy mã giao dịch: " + transCode;
                        WebUtils.WriteLogOutSite("NGANLUONG", "RECEICE", message);
                        msg = $"UserName: NGANLUONG \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    if (transDto.BILLED_AMOUNT.Value.CheckDecimalEx() != nganLuongResult.Price.CheckDecimalEx())
                    {
                        message = "Số tiền thanh toán của giao dịch: " + transCode + " không khớp";
                        WebUtils.WriteLogOutSite("NGANLUONG", "RECEICE", message);
                        msg = $"UserName: NGANLUONG \tDirection: RECEICE \tMessage: {message}";
                        SerilogHelper.WriteTraceLog(GetSiteId(), msg, typeof(CheckoutController), MethodBase.GetCurrentMethod()?.Name, transId);
                        TempData["Message"] = message;
                        return RedirectToAction("Index");
                    }

                    //xóa transCode ra khỏi Application
                    Session[transCode] = null;

                    PaymentInfoRequest request = new PaymentInfoRequest
                    {
                        TransactionCode = transCode,
                        PaidUser = UserCheckout().USER_ID,
                        PaidUserName = UserCheckout().USER_NAME,
                        PaymentRefNo = vpc_TransactionNo,
                        TransactionStatus = PAYMENT_STATUS.PAID,
                        PaymentMethod = res.PaymentMethod
                    };

                    _eportBillingGateway.UpdatePaymentInfo(GetSiteId(), request, GatewayAuthInfor.USER, GatewayAuthInfor.PASSWORD, transId);
                }
                else
                {
                    throw new ePortException("Dữ liệu nhận từ hệ thống thanh toán Ngân Lượng không hợp lệ.");
                }
            }
            catch (Exception ex)
            {
                SerilogHelper.WriteExceptionLog(ex, typeof(CheckoutController), MethodBase.GetCurrentMethod().Name, transId);
                message = ex.Message;
            }

            return RedirectToAction("Index");
        }
        #endregion Payment Ngân lượng
    }
}