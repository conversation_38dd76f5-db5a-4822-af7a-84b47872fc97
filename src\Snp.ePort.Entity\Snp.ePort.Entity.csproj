﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\packages\Microsoft.CodeAnalysis.PublicApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.PublicApiAnalyzers.props" Condition="Exists('..\..\packages\Microsoft.CodeAnalysis.PublicApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.PublicApiAnalyzers.props')" />
  <Import Project="..\..\packages\Microsoft.CodeAnalysis.BannedApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.BannedApiAnalyzers.props" Condition="Exists('..\..\packages\Microsoft.CodeAnalysis.BannedApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.BannedApiAnalyzers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{910C50B0-22E2-415C-97DE-435BB67A47E1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Snp.ePort.Entity</RootNamespace>
    <AssemblyName>Snp.ePort.Entity</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <DontImportPostSharp>True</DontImportPostSharp>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Snp.ePort.Entity.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Snp.ePort.Entity.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.DataAccess.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpo.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ACCOMPANIED_SERVICE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ADMIN_HELP_DESK_LOGS.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AgsEntity\AgsValidatationInfo.cs" />
    <Compile Include="AgsEntity\DocsInfo.cs" />
    <Compile Include="AgsEntity\MessageDetail.cs" />
    <Compile Include="AgsEntity\MessageHeader.cs" />
    <Compile Include="AgsEntity\MessageLevel.cs" />
    <Compile Include="AgsEntity\RespondMessage.cs" />
    <Compile Include="AgsEntity\RespondMessageUtil.cs" />
    <Compile Include="AM_ACCOUNT_TYPE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_ACTION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_ACTIVITY_LOG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_AGENT_CONFIG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_APPLOG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_AUTO_APPROVAL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_CATEGORY.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_CATEGORY_GROUP.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_CHE_TYPE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_CONFIG_CATEGORY.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_CONFIG_SETTING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_CONT_STATUS.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_DELIVERY_RECEIVE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_DISCOUNT_SETTING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_DISCOUNT_UNIT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_EMAIL_SMS_CONFIG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_EMAIL_TEMPLATE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_EPORT_CODE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_EPORT_SETTING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_FEATURE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_FULL_EMPTY.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_GROUP_MENU.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_GROUP_OPER_METHOD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_INVOICE_PATTERN.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_INVOICE_SERIAL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_INVOICE_STATUS.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_MENU.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_MENU_OPER_TYPE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_NOTIFICATION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_OPER_METHOD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_OPER_METHOD_CONFIG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_OPER_METHOD_SETTING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_OPER_METHOD_SP_ARISE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_OPER_TYPE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_PARTNER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_PAYMENT_METHOD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_ROLE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_ROLE_FEATURE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_ROLE_OPER_METHOD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SERVICE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SITE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SITE_GROUP_OPER_METHOD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SITE_INFO.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SITE_SETTING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SMS_TEMPLATE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SPECIAL_HDL_CODE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SYSTEM_CATEGORY.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_SYSTEM_CODE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_TRANSPORT_TYPE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_USER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_USER_AGENT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_USER_BACKUP.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_USER_LINER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_USER_SITE_ROLE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_USER_TOKEN.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="AM_VEHICLE_PERSONNELS.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_CUST_DECL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_DECLARE_TRANSPORT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_OPER_METHOD_DECL_TRANSP_TRK.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_SITE_MODULE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_SITE_PARAMS.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_SITE_SERVCE_URL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_SPECIAL_CODE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_SYS_CATEGORY_OPR_METHOD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CFG_TIME_PAYMENT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CHARGE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CHECK_IN_DETAIL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CHECK_IN_ITEM.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CHECK_IN_SHIP_INFO.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CONTAINER_VGM.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CURRENCY_UNIT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CUSTOMS_CARGO_ID.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CUSTOMS_DECLARATION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="CUSTOMS_EXCEPTION_CONTAINER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="DISTRICT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="DTO\CheckInOnline\ConfigApiCheckInNotice.cs" />
    <Compile Include="DTO\CheckInOnline\InstantNotify.cs" />
    <Compile Include="DTO\Billing\BillingChargeDto.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInDeleteResponse.cs" />
    <Compile Include="DTO\CheckInOnline\notificationRequest.cs" />
    <Compile Include="DTO\CheckInOnline\RequestDTO\ConfirmInforContRequest.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\AcceptRequestConnectRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\CheckExistGroupRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\DeleteConnectRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\DeleteGroupConnectRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\GetConnectionCodeRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\GetConnectionGroupRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\GetGroupConnectDetailRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\GetReceivingRequestConnectListRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\GetUserConnectDetailInfoRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\ResultRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\CheckinInforRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\RejectRequestConnectRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\RequestConnectRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\SaveGroupMemberRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\UpdateStatusConnectionRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\UpdateTruckNoByUserIdRes.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\UserConnectedRes.cs" />
    <Compile Include="DTO\CheckInOnline\SearchVehiclePersonCheckInOnlSummaryRequest.cs" />
    <Compile Include="DTO\CheckInOnline\SearchVehiclePersonCheckInOnlSummaryResponse.cs" />
    <Compile Include="DTO\CheckInOnline\SendMobileCheckInNoticeParam.cs" />
    <Compile Include="DTO\CheckInOnline\SendNoticeEportMobile.cs" />
    <Compile Include="DTO\CheckInOnline\UserConnectionDropDownTreeDto.cs" />
    <Compile Include="DTO\CheckInOnline\ResponseDTO\UserRequestConnectsRes.cs" />
    <Compile Include="DTO\DeclarationShipConfigModelDto.cs" />
    <Compile Include="DTO\Commodity\BulkUpdateModel.cs" />
    <Compile Include="DTO\Commodity\CommodityDetail.cs" />
    <Compile Include="DTO\Commodity\CommodityDto.cs" />
    <Compile Include="DTO\Commodity\CommodityRequest.cs" />
    <Compile Include="DTO\Commodity\CommodityViewModel.cs" />
    <Compile Include="DTO\Commodity\OrderDetail.cs" />
    <Compile Include="DTO\Commodity\OrderDetailCommodity.cs" />
    <Compile Include="DTO\Commodity\PreUpdateFdRequest.cs" />
    <Compile Include="DTO\Commodity\ResponseMessage.cs" />
    <Compile Include="DTO\ConfigSetting\AmConfigDto.cs" />
    <Compile Include="DTO\AmNotification\NotificationInputModel.cs" />
    <Compile Include="DTO\AmUserDto.cs" />
    <Compile Include="DTO\AM\OPER_METHOD_SETTING_DTO.cs" />
    <Compile Include="DTO\AM_ACTIVITY_LOG_DTO.cs" />
    <Compile Include="DTO\AM_CATEGORY_DTO.cs" />
    <Compile Include="DTO\AM_CONT_STATUS_DTO.cs" />
    <Compile Include="DTO\AM_EPORT_SETTING_DTO.cs" />
    <Compile Include="DTO\AmUserSiteRoleDto.cs" />
    <Compile Include="DTO\Billing\BillingCharge.cs" />
    <Compile Include="DTO\Billing\OrderDetailRequest.cs" />
    <Compile Include="DTO\Billing\GenBillingRequest.cs" />
    <Compile Include="DTO\Billing\PaymentChargeDto.cs" />
    <Compile Include="DTO\Billing\PaymentInfoRequest.cs" />
    <Compile Include="DTO\Billing\PaymentInfoResponse.cs" />
    <Compile Include="DTO\Billing\PaymentTransactionDto.cs" />
    <Compile Include="DTO\CHARGE_DTO.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInSmsAccountDto.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInInfoDetailDto.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInInfoDto.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInOnlineDetailDto.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInOnlineDto.cs" />
    <Compile Include="DTO\CheckInOnline\InvalidCheckInDto.cs" />
    <Compile Include="DTO\CheckInOnline\EditOrderDetailNoDto.cs" />
    <Compile Include="DTO\CheckInOnline\CheckInAccountDto.cs" />
    <Compile Include="DTO\CheckInOnline\EditOrderDetailRequest.cs" />
    <Compile Include="DTO\ClosingTime\ClosingTimeHeaderReportModel.cs" />
    <Compile Include="DTO\ClosingTime\ContainerDeclarationReportDto.cs" />
    <Compile Include="DTO\Checkout\CartDto.cs" />
    <Compile Include="DTO\Checkout\CustomerDto.cs" />
    <Compile Include="DTO\Checkout\OrderDetailDebitDto.cs" />
    <Compile Include="DTO\CompanyInfo\CompanyInfoModel.cs" />
    <Compile Include="DTO\ConfigSetting\DataResult.cs" />
    <Compile Include="DTO\ConfigSiteUrl\UrlConfigSite.cs" />
    <Compile Include="DTO\ConnectionManagement\ConnectionManagementCondition.cs" />
    <Compile Include="DTO\ConnectionManagement\GroupConnectDTO.cs" />
    <Compile Include="DTO\ConnectionManagement\UserConnectDTO.cs" />
    <Compile Include="DTO\ContainerDto.cs" />
    <Compile Include="DTO\ConvertToIsoDto.cs" />
    <Compile Include="DTO\CustomsDeclarationInfoDto.cs" />
    <Compile Include="DTO\CustomsDeclaration\CustomDeclarationNoteConfigDto.cs" />
    <Compile Include="DTO\CustomsDeclaration\CustomsDeclarationDto.cs" />
    <Compile Include="DTO\CustomsDeclaration\CustomsDeclarationInputModel.cs" />
    <Compile Include="DTO\CustomsDeclaration\CustomsDeclarationInYard.cs" />
    <Compile Include="DTO\CustomsDeclaration\CustomsDeclarationInYardRequest.cs" />
    <Compile Include="DTO\CustomsInfo\CustDeclNoInfoDto.cs" />
    <Compile Include="DTO\CustomsInfo\DeclarationDetail.cs" />
    <Compile Include="DTO\CustomsInfo\InfrasFeeDto.cs" />
    <Compile Include="DTO\CustomsInfo\InfrasFeeResponse.cs" />
    <Compile Include="DTO\CustomsInfo\CustomsDetail.cs" />
    <Compile Include="DTO\CutSealService\CutSealServiceDTO.cs" />
    <Compile Include="DTO\CutSealService\CutSealServiceImportDTO.cs" />
    <Compile Include="DTO\CutSealService\CutSealTypeDTO.cs" />
    <Compile Include="DTO\EBookingValiteDateDto.cs" />
    <Compile Include="DTO\EBooking\ContainerRegisterDetailDto.cs" />
    <Compile Include="DTO\EBooking\ContainerRegisterDto.cs" />
    <Compile Include="DTO\EBooking\EbookingSearchDto.cs" />
    <Compile Include="DTO\EInvoice\ConfigSendNoticeErr.cs" />
    <Compile Include="DTO\EBooking\OrderDetailEbk.cs" />
    <Compile Include="DTO\EirReportModel\AbstractMeta.cs" />
    <Compile Include="DTO\EirReportModel\BaseChiTietPhieuEir.cs" />
    <Compile Include="DTO\EirReportModel\BaseModel.cs" />
    <Compile Include="DTO\EirReportModel\BaseRequest.cs" />
    <Compile Include="DTO\EirReportModel\BaseResponse.cs" />
    <Compile Include="DTO\EirReportModel\DichVuDongRutDetail.cs" />
    <Compile Include="DTO\EirReportModel\DichVuDongRutModel.cs" />
    <Compile Include="DTO\EirReportModel\DichVuDongRutRequest.cs" />
    <Compile Include="DTO\EirReportModel\DichVuDongRutResponse.cs" />
    <Compile Include="DTO\EirReportModel\Error.cs" />
    <Compile Include="DTO\EirReportModel\GiaoContChoCangDetail.cs" />
    <Compile Include="DTO\EirReportModel\GiaoContChoCangModel.cs" />
    <Compile Include="DTO\EirReportModel\GiaoContChoCangRequest.cs" />
    <Compile Include="DTO\EirReportModel\GiaoContChoCangResponse.cs" />
    <Compile Include="DTO\EirReportModel\IBaseResponse.cs" />
    <Compile Include="DTO\EirReportModel\Meta.cs" />
    <Compile Include="DTO\EirReportModel\MultiRequest.cs" />
    <Compile Include="DTO\EirReportModel\NhanContTuCangDetail.cs" />
    <Compile Include="DTO\EirReportModel\NhanContTuCangModel.cs" />
    <Compile Include="DTO\EirReportModel\NhanContTuCangRequest.cs" />
    <Compile Include="DTO\EirReportModel\NhanContTuCangResponse.cs" />
    <Compile Include="DTO\EirReportModel\PrintType.cs" />
    <Compile Include="DTO\Eir\EirDetail.cs" />
    <Compile Include="DTO\Eir\EirDTO.cs" />
    <Compile Include="DTO\Eir\PrintEirConfigDto.cs" />
    <Compile Include="DTO\Eir\SiteFullNameDto.cs" />
    <Compile Include="DTO\Eir\SloganDto.cs" />
    <Compile Include="DTO\Eir\StripStuffTruckDto.cs" />
    <Compile Include="DTO\EmptyContainerDelivery\ECDOrderDetailDTO.cs" />
    <Compile Include="DTO\EmptyContainerDelivery\EmptyContainerDeliveryDTO.cs" />
    <Compile Include="DTO\EmptyContainerDelivery\EmptyContainerDeliveryImportDTO.cs" />
    <Compile Include="DTO\EmptyContainerReceiving\EbookingCondition.cs" />
    <Compile Include="DTO\EmptyContainerDelivery\EmptyEDIContDeliveryViewModel.cs" />
    <Compile Include="DTO\EmptyContainerDelivery\EmptyContDeliveryLookupResultViewModel.cs" />
    <Compile Include="DTO\EmptyContainerDelivery\EmptyReceivDetailCondition.cs" />
    <Compile Include="DTO\EmptyContainerReceiving\EBookingConfirmDto.cs" />
    <Compile Include="DTO\EmptyContainerReceiving\EBookingUpdateRegistedDto.cs" />
    <Compile Include="DTO\EmptyContainerReceiving\EmptyContainerReceivingDto.cs" />
    <Compile Include="DTO\EmptyContainerReceiving\EmptyContReceiveImportDto.cs" />
    <Compile Include="DTO\EmptyContainerReceiving\EmptyReceivingValidateDto .cs" />
    <Compile Include="DTO\EmptyContainerReceiving\OrderDetailEBookingDto.cs" />
    <Compile Include="DTO\EportVesselPortDetailDTO.cs" />
    <Compile Include="DTO\Feature\ViewFeatureDto.cs" />
    <Compile Include="DTO\FullContainerDelivery\ConfigGateInRequestContRf.cs" />
    <Compile Include="DTO\FullContainerDelivery\ContDelvErrorModel.cs" />
    <Compile Include="DTO\FullContainerDelivery\ContDelvRequest.cs" />
    <Compile Include="DTO\FullContainerDelivery\ContDelvResponse.cs" />
    <Compile Include="DTO\FullContainerDelivery\DataEdit.cs" />
    <Compile Include="DTO\FullContainerDelivery\SiteApplyCommodityDto.cs" />
    <Compile Include="DTO\FullContainerDelivery\FcdImportRequest.cs" />
    <Compile Include="DTO\FullContainerDelivery\FCDOrderDetailDTO.cs" />
    <Compile Include="DTO\FullContainerDelivery\FullContainerDeliveryDTO.cs" />
    <Compile Include="DTO\FullContainerDelivery\FullContainerDeliveryImportDTO.cs" />
    <Compile Include="DTO\FullContainerReceiving\ContainerRequestDto.cs" />
    <Compile Include="DTO\FullContainerReceiving\FullContainerInfo.cs" />
    <Compile Include="DTO\FullContainerReceiving\FullContainerReceivingDTO.cs" />
    <Compile Include="DTO\FullContainerReceiving\FullContainerReceivingImportDTO.cs" />
    <Compile Include="DTO\FullContainerReceiving\FullContainerReceivingOverSize.cs" />
    <Compile Include="DTO\FullContainerReceiving\ORDER_DETAIL_RESULT.cs" />
    <Compile Include="DTO\AM_DISCOUNT_SETTING_DTO.cs" />
    <Compile Include="DTO\AM_DISCOUNT_UNIT_DTO.cs" />
    <Compile Include="DTO\AM_GROUP_OPER_METHOD_DTO.cs" />
    <Compile Include="DTO\AM_INVOICE_SERIAL_DTO.cs" />
    <Compile Include="DTO\AM_OPER_METHOD_DTO.cs" />
    <Compile Include="DTO\Hazadous\HazClassInformationDto.cs" />
    <Compile Include="DTO\Invoices\PreviewInvoiceModel.cs" />
    <Compile Include="DTO\Invoices\RoleActionInvoice.cs" />
    <Compile Include="DTO\Home\UpdatePwdDTO.cs" />
    <Compile Include="DTO\Hazadous\HazadousMasterInfoDTO.cs" />
    <Compile Include="DTO\Hazadous\ViewHazMasterInfoDTO.cs" />
    <Compile Include="DTO\Keycloak\ConfigVerifyKeycloak.cs" />
    <Compile Include="DTO\Keycloak\KeycloakOption.cs" />
    <Compile Include="DTO\Keycloak\Token.cs" />
    <Compile Include="DTO\Keycloak\UserInfo.cs" />
    <Compile Include="DTO\REPORT_CHECKIN_ONLINE_DTO.cs" />
    <Compile Include="DTO\ServiceAttachment\GrossWeightServiceTCLDetail.cs" />
    <Compile Include="DTO\NganLuong\NganLuongMerchantInfo.cs" />
    <Compile Include="DTO\NganLuong\NganLuongPaymentResultResponse.cs" />
    <Compile Include="DTO\NganLuong\NganLuongRefundResponse.cs" />
    <Compile Include="DTO\NganLuong\NganLuongResponseCode.cs" />
    <Compile Include="DTO\NganLuong\NganLuongResult.cs" />
    <Compile Include="DTO\NganLuong\PaymentNganLuongResponse.cs" />
    <Compile Include="DTO\NganLuong\TokenNganLuongResponse.cs" />
    <Compile Include="DTO\User\KeyValueAccountModel.cs" />
    <Compile Include="DTO\ManageVehiclePersonnel\ManageVehiclePersonnelDto.cs" />
    <Compile Include="DTO\ManageVehiclePersonnel\SearchManageVehiclePersonnelCondition.cs" />
    <Compile Include="DTO\Notification\ConfigApiUrl.cs" />
    <Compile Include="DTO\Notification\NotificationModel.cs" />
    <Compile Include="DTO\OperMethod\ViewOperMethod.cs" />
    <Compile Include="DTO\Core\BaseServiceResponse.cs" />
    <Compile Include="DTO\OrdeDetailPaidDto.cs" />
    <Compile Include="DTO\EBooking\EBookingDetailDto.cs" />
    <Compile Include="DTO\EBooking\EBookingDto.cs" />
    <Compile Include="DTO\PaymentRequest\DebitCustomerInfo.cs" />
    <Compile Include="DTO\PaymentRequest\DebitCustomerInfor.cs" />
    <Compile Include="DTO\PaymentRequest\DebitPaymentRequestCondition.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestGeneralsExportDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestGeneralsExportEnDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestGeneralsDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestDetailDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestGeneralDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestGeneralListDto.cs" />
    <Compile Include="DTO\PaymentRequest\PaymentRequestHistoryDto.cs" />
    <Compile Include="DTO\PaymentRequest\UserLocked.cs" />
    <Compile Include="DTO\ORDER_DTO.cs" />
    <Compile Include="DTO\RegisterDebitOnlineDto.cs" />
    <Compile Include="DTO\RegisterPaymentList\RegisterPaymentListDTO.cs" />
    <Compile Include="DTO\RegisterPaymentList\RgtPymContainerDetailDTO.cs" />
    <Compile Include="DTO\ServiceAttachment\GrossWeighReceiptDto.cs" />
    <Compile Include="DTO\ServiceAttachment\OrderDetailDto.cs" />
    <Compile Include="DTO\ServiceAttachment\PaymentInfo.cs" />
    <Compile Include="DTO\ServiceAttachment\ResponseServiceAttach.cs" />
    <Compile Include="DTO\ServiceAttachment\RoleAction.cs" />
    <Compile Include="DTO\ServiceAttachment\ServiceAttachmentDto.cs" />
    <Compile Include="DTO\ServiceAttachment\ServiceAttachmentRequest.cs" />
    <Compile Include="DTO\SiteDto.cs" />
    <Compile Include="DTO\BaseDTO.cs" />
    <Compile Include="DTO\CFG_SYS_CATEGORY_OPR_METHOD_DTO.cs" />
    <Compile Include="DTO\ChargeDTO.cs" />
    <Compile Include="DTO\ContainerInfo\ContainerDetailDTO.cs" />
    <Compile Include="DTO\CustomsDeclarationDto.cs" />
    <Compile Include="DTO\DataResponse.cs" />
    <Compile Include="DTO\DiscountSetting.cs" />
    <Compile Include="DTO\EDOInfoDTO.cs" />
    <Compile Include="DTO\INVOICE_DETAILS_DTO.cs" />
    <Compile Include="DTO\INVOICE_DTO.cs" />
    <Compile Include="DTO\INVOICE_ERROR_DTO.cs" />
    <Compile Include="DTO\ItemInfo\ItemInfo.cs" />
    <Compile Include="DTO\LlPodsDTO.cs" />
    <Compile Include="DTO\Loyalty\CheckingLoyaltyRequest.cs" />
    <Compile Include="DTO\Loyalty\CheckingLoyaltyResponse.cs" />
    <Compile Include="DTO\Loyalty\EportInvoiceReconcilationResponse.cs" />
    <Compile Include="DTO\Loyalty\EportInvoiceResponse.cs" />
    <Compile Include="DTO\Loyalty\EportLoyalInvoiceResponse.cs" />
    <Compile Include="DTO\Loyalty\IntergrateInvoiceRequest.cs" />
    <Compile Include="DTO\Loyalty\IntergrateInvoiceResponse.cs" />
    <Compile Include="DTO\Loyalty\InvoiceLineIntegrationModel.cs" />
    <Compile Include="DTO\Loyalty\LoyalEportInvoiceRecilationResponse.cs" />
    <Compile Include="DTO\Loyalty\LoyalInvoiceRecilationResponse.cs" />
    <Compile Include="DTO\Loyalty\LoyalInvoiceResponse.cs" />
    <Compile Include="DTO\Loyalty\QueryAvailableResponse.cs" />
    <Compile Include="DTO\Loyalty\TopoLoyalIntegrationResponse.cs" />
    <Compile Include="DTO\MoveContainerDTO.cs" />
    <Compile Include="DTO\MoveContainer\ContainerInfo.cs" />
    <Compile Include="DTO\MoveContainer\ContainerInfoTopo.cs" />
    <Compile Include="DTO\MoveContainer\CreateMoveContainerDTO.cs" />
    <Compile Include="DTO\MoveContainer\MoveContainerExcelDTO.cs" />
    <Compile Include="DTO\MoveContainer\OrderDetailContMovementDto.cs" />
    <Compile Include="DTO\Napas\PayLaterUpdateStatus.cs" />
    <Compile Include="DTO\Napas\PayLaterTransaction.cs" />
    <Compile Include="DTO\Napas\Acquirer.cs" />
    <Compile Include="DTO\Napas\AuthorizationResponse.cs" />
    <Compile Include="DTO\Napas\Card.cs" />
    <Compile Include="DTO\Napas\DataKeyRequest.cs" />
    <Compile Include="DTO\Napas\DataKeyResponse.cs" />
    <Compile Include="DTO\Napas\Error.cs" />
    <Compile Include="DTO\Napas\InputParameters.cs" />
    <Compile Include="DTO\Napas\MerchantInfo.cs" />
    <Compile Include="DTO\Napas\NapasHashResult.cs" />
    <Compile Include="DTO\Napas\NapasResult.cs" />
    <Compile Include="DTO\Napas\Order.cs" />
    <Compile Include="DTO\Napas\PaymentResult.cs" />
    <Compile Include="DTO\Napas\Provided.cs" />
    <Compile Include="DTO\Napas\QueryDrResponse.cs" />
    <Compile Include="DTO\Napas\RefundRequest.cs" />
    <Compile Include="DTO\Napas\RefundResponse.cs" />
    <Compile Include="DTO\Napas\Response.cs" />
    <Compile Include="DTO\Napas\SourceOfFunds.cs" />
    <Compile Include="DTO\Napas\TokenRequest.cs" />
    <Compile Include="DTO\Napas\TokenResponse.cs" />
    <Compile Include="DTO\Napas\Transaction.cs" />
    <Compile Include="DTO\OrderDetailDocumentDto.cs" />
    <Compile Include="DTO\OperMethodSettingDto.cs" />
    <Compile Include="DTO\ORDER_DETAIL_DOCUMENT_DTO.cs" />
    <Compile Include="DTO\ORDER_DETAIL_DTO.cs" />
    <Compile Include="DTO\ORDER_DETAIL_TRUCK_DTO.cs" />
    <Compile Include="DTO\OTM\OTM_CONT_STATUS_DTO.cs" />
    <Compile Include="DTO\OTM\OTM_VOYAGE_DTO.cs" />
    <Compile Include="DTO\PaymentList\PaymentListDetailDto.cs" />
    <Compile Include="DTO\PaymentList\PaymentListDto.cs" />
    <Compile Include="DTO\EmailContent.cs" />
    <Compile Include="DTO\ReeferMonitorConfirmation\ContainerReeferRequest.cs" />
    <Compile Include="DTO\ReeferMonitorConfirmation\ReeferMonitorConfirmationDto.cs" />
    <Compile Include="DTO\ReeferMonitorConfirmation\ReeferMonitorConfirmResponse.cs" />
    <Compile Include="DTO\ReeferMonitorConfirmation\ConfrimReeferRequest.cs" />
    <Compile Include="DTO\ReeferMonitorConfirmation\RmcInformation.cs" />
    <Compile Include="DTO\ReeferMonitorConfirmation\RmcModel.cs" />
    <Compile Include="DTO\Tdr\VesselCranesProductivity.cs" />
    <Compile Include="DTO\Tdr\VesselHandlingInfomation.cs" />
    <Compile Include="DTO\Tdr\VesselHandlingInfomationModel.cs" />
    <Compile Include="DTO\Tdr\VesselProductionInfo.cs" />
    <Compile Include="DTO\TransactionReport\TransactionReportDto.cs" />
    <Compile Include="DTO\TransactionReport\TransactionReportFilter.cs" />
    <Compile Include="DTO\SearchContainerInformation\SearchContainerInforDto.cs" />
    <Compile Include="DTO\SearchContainerInformation\SearchContInfor.cs" />
    <Compile Include="DTO\SearchContainerInformation\SearchContStatistics.cs" />
    <Compile Include="DTO\Shipping\AttachExcel.cs" />
    <Compile Include="DTO\Shipping\ContainerCancelDetail.cs" />
    <Compile Include="DTO\Shipping\ContainerConfirmDetail.cs" />
    <Compile Include="DTO\Shipping\ContainerShippingDetail.cs" />
    <Compile Include="DTO\Shipping\DepotResponse.cs" />
    <Compile Include="DTO\Shipping\EmailContentConfirmShipping.cs" />
    <Compile Include="DTO\Shipping\ErrorDetail.cs" />
    <Compile Include="DTO\Shipping\InvoiceDetail.cs" />
    <Compile Include="DTO\Shipping\LineOperResponse.cs" />
    <Compile Include="DTO\Shipping\OrderDetailLogisticDTO.cs" />
    <Compile Include="DTO\Shipping\RegisteredShippingDTO.cs" />
    <Compile Include="DTO\Shipping\RevenueReportDTO.cs" />
    <Compile Include="DTO\Shipping\SearchShippingContainer.cs" />
    <Compile Include="DTO\Shipping\ShippingDTO.cs" />
    <Compile Include="DTO\Shipping\ShippingRegisterContainer.cs" />
    <Compile Include="DTO\Sms\AmSmsTemplateDto.cs" />
    <Compile Include="DTO\Sms\SmsTemplateDto.cs" />
    <Compile Include="DTO\SysCodeDto.cs" />
    <Compile Include="DTO\RegisteredVesselPort.cs" />
    <Compile Include="DTO\ResultModel.cs" />
    <Compile Include="DTO\SearchContainerKeysDTO.cs" />
    <Compile Include="DTO\SMSDTO.cs" />
    <Compile Include="DTO\SystemCodesDto.cs" />
    <Compile Include="DTO\TRANSACTION_DTO.cs" />
    <Compile Include="DTO\TRANSACTION_ERROR_DTO.cs" />
    <Compile Include="DTO\TransportAgent.cs" />
    <Compile Include="DTO\TransportationUnit\AssignTransportDetailDTO.cs" />
    <Compile Include="DTO\TransportationUnit\AssignTransportDTO.cs" />
    <Compile Include="DTO\TransportationUnit\CFG_TRANSPORT_BY_BATCH.cs" />
    <Compile Include="DTO\TransportationUnit\ContainerShippingInfoDetailDTO.cs" />
    <Compile Include="DTO\TransportationUnit\ContainerShippingInfoDTO.cs" />
    <Compile Include="DTO\TransportationUnit\CONT_SUMMARY.cs" />
    <Compile Include="DTO\TransportationUnit\OrderTruckDto.cs" />
    <Compile Include="DTO\TransportationUnit\ORDER_LIST.cs" />
    <Compile Include="DTO\TransportationUnit\ORDER_TRUCK_VIEW.cs" />
    <Compile Include="DTO\TransportationUnit\TransportationUnit.cs" />
    <Compile Include="DTO\TransportationUnit\TRANS_UNIT_DETAIL.cs" />
    <Compile Include="DTO\TruckViolation\TransportAndOrderOwnerDto.cs" />
    <Compile Include="DTO\TruckViolation\ViolationRequest.cs" />
    <Compile Include="DTO\TRUCK_DTO.cs" />
    <Compile Include="DTO\User\ChangePasswordDto.cs" />
    <Compile Include="DTO\User\AccountManegementDto.cs" />
    <Compile Include="DTO\User\EmailContentUser.cs" />
    <Compile Include="DTO\User\UserDto.cs" />
    <Compile Include="DTO\UserTokenDto.cs" />
    <Compile Include="DTO\USER_DTO.cs" />
    <Compile Include="DTO\VesselOutVoyageDTO.cs" />
    <Compile Include="DTO\VesselPortChangeChecking.cs" />
    <Compile Include="DTO\VesselPortChangeDTO.cs" />
    <Compile Include="DTO\VesselPortChange\EmailContentAutoRejectTdtc.cs" />
    <Compile Include="DTO\VesselPortChange\EmailContentFreeRequest.cs" />
    <Compile Include="DTO\VesselPortChange\OrderDetailVesselPortChangeFilter.cs" />
    <Compile Include="DTO\VesselPortChange\OrderDetailVesselPortDto.cs" />
    <Compile Include="DTO\VesselPortChange\RegisteredVesseOutVoyageInOrderDetailDto.cs" />
    <Compile Include="DTO\VesselPortChange\VesselLLDischPortRequest.cs" />
    <Compile Include="DTO\VesselPortChange\VesselPortChangeEditRequest.cs" />
    <Compile Include="DTO\VesselPortChange\VesselPortChangeRegisteredDto.cs" />
    <Compile Include="DTO\VesselPortChange\VesselPortChangeRegisterRequest.cs" />
    <Compile Include="DTO\VesselPortChange\VesselPortChangeResponse.cs" />
    <Compile Include="DTO\VesselPortDetailDTO.cs" />
    <Compile Include="DTO\VesVoyageLlPodChangeServices\VesVoyageLlPodChangeServiceDTO.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\AutoRejectVesPortChangeDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\FreeVesselPortChangeDetailDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\RegisteredVesselLlPodDetailDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\RegisteredVesselLlPodDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\ServiceConfigDetail.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\ServiceConfigDetailDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\ServicesDTO.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\UserDTO.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\ValidatePaymentVesselPortChangeDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesselPortChangeFillter.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesselsDTO.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesselVoyagePortChangeDetailDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesselVoyagePortChangeDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesVoyageLlPodChangeSearch.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesVoyagesDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesVoyagesLlPodChangeDetailDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesVoyagesLlPodChangesDto.cs" />
    <Compile Include="DTO\VesVoyageLlPodChanges\VesVoyChangeServiceDto.cs" />
    <Compile Include="DTO\VesVoyPortServicesDTO.cs" />
    <Compile Include="DTO\Vgm\ContainerWeight.cs" />
    <Compile Include="DTO\Vgm\ContainerWeightRequest.cs" />
    <Compile Include="DTO\ViolateSeaportSecurity\ANCBDto.cs" />
    <Compile Include="DTO\ViolateSeaportSecurity\ViolationInfoCustDto.cs" />
    <Compile Include="DTO\YardServices\CheckYardServiceContainerDTO.cs" />
    <Compile Include="DTO\YardServices\CheckYardServiceContainerResponse.cs" />
    <Compile Include="DTO\YardServices\YardOfServiceDTO.cs" />
    <Compile Include="DTO\YardServices\YardOfServiceImportDto.cs" />
    <Compile Include="DTO\YardServices\OrderDetailMultiUpdate.cs" />
    <Compile Include="DTO\YardServices\YardRegisterService.cs" />
    <Compile Include="EntityPartial\ADMIN_HELP_DESK_LOGS.cs" />
    <Compile Include="EntityPartial\AM_VEHICLE_PERSONNELS.cs" />
    <Compile Include="EntityPartial\CFG_CUST_DECL.Partial.cs" />
    <Compile Include="DTO\YardServices\YardServiceRequestDto.cs" />
    <Compile Include="EntityPartial\AM_AGENT_CONFIG.Partial.cs" />
    <Compile Include="EntityPartial\AM_CONFIG_CATEGORY.cs" />
    <Compile Include="EntityPartial\AM_CONFIG_SETTING.cs" />
    <Compile Include="EntityPartial\AM_USER_TOKEN.Partial.cs" />
    <Compile Include="EntityPartial\CHECK_IN_DETAIL.Partial.cs" />
    <Compile Include="EntityPartial\AM_EPORT_CODE.cs" />
    <Compile Include="EntityPartial\AM_EPORT_SETTING.Partial.cs" />
    <Compile Include="EntityPartial\AM_MENU_OPER_TYPE.Partial.cs" />
    <Compile Include="EntityPartial\AM_OPER_METHOD_CONFIG.Partial.cs" />
    <Compile Include="EntityPartial\AM_OPER_METHOD_SP_ARISE.Partial.cs" />
    <Compile Include="EntityPartial\AM_PAYMENT_METHOD.Partial.cs" />
    <Compile Include="EntityPartial\AM_ROLE_OPER_METHOD.Partial.cs" />
    <Compile Include="EntityPartial\AM_SMS_TEMPLATE.Partial.cs" />
    <Compile Include="EntityPartial\CFG_DECLARE_TRANSPORT.Partial.cs" />
    <Compile Include="EntityPartial\CFG_SPECIAL_CODE.Partial.cs" />
    <Compile Include="EntityPartial\CFG_TIME_PAYMENT.cs" />
    <Compile Include="EntityPartial\CHECK_IN_INFOR_WARNING.cs" />
    <Compile Include="EntityPartial\CHECK_IN_SHIP_INFO.Partial.cs" />
    <Compile Include="EntityPartial\DISTRICT.Partial.cs" />
    <Compile Include="EntityPartial\GET_ORDERS_LIST_BY_METHOD_Result.Partial.cs" />
    <Compile Include="EntityPartial\CURRENCY_UNIT.cs" />
    <Compile Include="EntityPartial\EXCHANGE_RATE.cs" />
    <Compile Include="EntityPartial\AM_CHE_TYPE.cs" />
    <Compile Include="EntityPartial\AM_CONT_STATUS.cs" />
    <Compile Include="EntityPartial\AM_EMAIL_TEMPLATE.Partial.cs" />
    <Compile Include="EntityPartial\AM_EMAIL_SMS_CONFIG.Partial.cs" />
    <Compile Include="EntityPartial\AM_INVOICE_SERIAL.cs" />
    <Compile Include="EntityPartial\AM_INVOICE_PATTERN.cs" />
    <Compile Include="EntityPartial\AM_NOTIFICATION.cs" />
    <Compile Include="EntityPartial\AM_NOTIFICATION_SETTING.cs" />
    <Compile Include="EntityPartial\AM_AUTO_APPROVAL.cs" />
    <Compile Include="EntityPartial\AM_SERVICE.Partial.cs" />
    <Compile Include="EntityPartial\AM_SITE_INFO.Partial.cs" />
    <Compile Include="EntityPartial\AM_SPECIAL_HDL_CODE.Partial.cs" />
    <Compile Include="EntityPartial\AM_SYSTEM_CATEGORY.cs" />
    <Compile Include="EntityPartial\GROUP_CONNECTION.Partial.cs" />
    <Compile Include="EntityPartial\GROUP_MEMBER.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_CHECK_IN.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_COMMODITY.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_CONT_MOVEMENT.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_DOCUMENT.cs" />
    <Compile Include="EntityPartial\ITEM_RATE.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_EBOOKING.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_LOGISTIC.Patial.cs" />
    <Compile Include="EntityPartial\ORDER.Partial - Copy.cs" />
    <Compile Include="EntityPartial\ORDER_TRUCK.partial.cs" />
    <Compile Include="EntityPartial\PR_GET_SERVICE_ATTACH_EXEC_Result.cs" />
    <Compile Include="EntityPartial\TRANSACTION_DEBIT_COMPLETED.cs" />
    <Compile Include="EntityPartial\VESSEL_VOYAGE_PORT_CHANGE_SERVICES.cs" />
    <Compile Include="EntityPartial\VE_OPER_METHOD_SETTING.Partial.cs" />
    <Compile Include="EntityPartial\VE_ORDER_DETAIL.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_PORT_CHANGE.cs" />
    <Compile Include="EntityPartial\SERVICE_CONFIG.Partial.cs" />
    <Compile Include="EntityPartial\CFG_SITE_MODULE.cs" />
    <Compile Include="EntityPartial\CFG_SITE_SERVCE_URL.cs" />
    <Compile Include="EntityPartial\CFG_SYS_CATEGORY_OPR_METHOD.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_IMO.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_OTP.cs" />
    <Compile Include="EntityPartial\TRUCK_TRACKING.Partial.cs" />
    <Compile Include="EntityPartial\AM_ACCOUNT_TYPE.cs" />
    <Compile Include="EntityPartial\AM_ACTIVITY_LOG.cs" />
    <Compile Include="EntityPartial\AM_CATEGORY.cs" />
    <Compile Include="EntityPartial\AM_CATEGORY_GROUP.cs" />
    <Compile Include="EntityPartial\AM_DELIVERY_RECEIVE.cs" />
    <Compile Include="EntityPartial\AM_DISCOUNT_SETTING.cs" />
    <Compile Include="EntityPartial\AM_DISCOUNT_UNIT.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_TRUCK.Partial.cs" />
    <Compile Include="EntityPartial\TRUCK.Partial.cs" />
    <Compile Include="EntityPartial\CFG_OPER_METHOD_DECL_TRANSP_TRK.Partial.cs" />
    <Compile Include="EntityPartial\CUSTOMS_EXCEPTION_CONTAINER.cs" />
    <Compile Include="EntityPartial\AM_FEATURE.cs" />
    <Compile Include="EntityPartial\AM_FULL_EMPTY.cs" />
    <Compile Include="EntityPartial\AM_GROUP_MENU.cs" />
    <Compile Include="EntityPartial\AM_GROUP_OPER_METHOD.cs" />
    <Compile Include="EntityPartial\AM_MENU.cs" />
    <Compile Include="EntityPartial\AM_OPER_METHOD.cs" />
    <Compile Include="EntityPartial\AM_OPER_METHOD_SETTING.cs" />
    <Compile Include="EntityPartial\AM_ROLE_FEATURE.cs" />
    <Compile Include="EntityPartial\AM_SITE.Partial.cs" />
    <Compile Include="EntityPartial\AM_SITE_GROUP_OPER_METHOD.cs" />
    <Compile Include="EntityPartial\AM_SITE_SETTING.cs" />
    <Compile Include="EntityPartial\AM_USER_SITE_ROLE.cs" />
    <Compile Include="EntityPartial\CUSTOMS_CARGO_ID.cs" />
    <Compile Include="EntityPartial\CUSTOMS_DECLARATION.cs" />
    <Compile Include="EntityPartial\EXTRA_PHONE_NUMBER.Partial.cs" />
    <Compile Include="EntityPartial\AM_ROLE_MENU.Partial.cs" />
    <Compile Include="EntityPartial\AM_OPER_TYPE.cs" />
    <Compile Include="EntityPartial\INVOICE_DETAIL.Partial.cs" />
    <Compile Include="EntityPartial\INVOICE.Partial.cs" />
    <Compile Include="EntityPartial\ContainerVgm.Partial.cs" />
    <Compile Include="EntityPartial\CFG_SITE_PARAMS.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_OOG.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_SERVICE.Partial.cs" />
    <Compile Include="EntityPartial\TRANSACTION.Partial.cs" />
    <Compile Include="EntityPartial\AM_TRANSPORT_TYPE.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL_PROPERTY.Partial.cs" />
    <Compile Include="EntityPartial\ACCOMPANIED_SERVICE.cs" />
    <Compile Include="EntityPartial\TRANSPORT.cs" />
    <Compile Include="EntityPartial\TRANSPORT_DETAIL.cs" />
    <Compile Include="EntityPartial\USER_ACTIVATION.cs" />
    <Compile Include="EntityPartial\USER_OPER_METHOD.Partial.cs" />
    <Compile Include="EntityPartial\AM_USERROLE.Partial.cs" />
    <Compile Include="EntityPartial\AM_ROLE.Partial.cs" />
    <Compile Include="EntityPartial\CHARGE.Partial.cs" />
    <Compile Include="Core\IOrderContextEx.cs" />
    <Compile Include="EntityPartial\OrderEntities.Partial.cs" />
    <Compile Include="EntityPartial\AM_USER.Partial.cs" />
    <Compile Include="Core\AuditableEntity.cs" />
    <Compile Include="Core\Entity.cs" />
    <Compile Include="EntityPartial\ERROR_DESC.Partial.cs" />
    <Compile Include="Core\IAuditableEntity.cs" />
    <Compile Include="Core\IEntity.cs" />
    <Compile Include="EntityPartial\ORDER.Partial.cs" />
    <Compile Include="EntityPartial\ORDER_DETAIL.Partial.cs" />
    <Compile Include="EntityPartial\VESSEL_VOYAGE_PORT_CHANGE.cs" />
    <Compile Include="EntityPartial\VESSEL_VOYAGE_PORT_CHANGE_DETAIL.cs" />
    <Compile Include="EntityPartial\VIEW_VES_VOYAGES_LLPOD_CHANGE_DETAIL.Partial.cs" />
    <Compile Include="EntityPartial\WARD.Partial.cs" />
    <Compile Include="Enums.cs" />
    <Compile Include="ERROR_DESC.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="EXCHANGE_RATE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="EXTRA_PHONE_NUMBER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GenerateNextNumber_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_AM_OPER_METHOD_SETTING_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_EPORT_INVOICE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_LIST_CONT_REGISTED_CARGO_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_LIST_CONT_REGISTED_DECL_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_ORDERS_LIST_BY_METHOD_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_REGISTER_DEBIT_LIST_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GET_TRANSACTION_TO_COMPARE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GP_SEQUENCE_GEN.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GROUP_CONNECTION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="GROUP_MEMBER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="INVOICE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="INVOICE_DETAILS.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="Model\AdressModel.cs" />
    <Compile Include="Model\CheckInValidatorResponeModel.cs" />
    <Compile Include="Model\CreateUserCheckInModel.cs" />
    <Compile Include="Model\CtccConfirmReport.cs" />
    <Compile Include="Model\CustomDeclarationReportModel.cs" />
    <Compile Include="Model\DebitNoteReportModel.cs" />
    <Compile Include="Model\CutSealReportModel.cs" />
    <Compile Include="Model\DownloadInvoiceSearchModel.cs" />
    <Compile Include="Model\DVCCModel.cs" />
    <Compile Include="Model\CTCCDetailModel.cs" />
    <Compile Include="Model\EdiMappingModel.cs" />
    <Compile Include="Model\EmailContentANCBModel.cs" />
    <Compile Include="Model\EmailContentModel.cs" />
    <Compile Include="Model\ExcelContainerShippingColumn.cs" />
    <Compile Include="Model\MainchargeConfigModel.cs" />
    <Compile Include="Model\NotRequireSealNoDto.cs" />
    <Compile Include="Model\OrderDetailCheckInModel.cs" />
    <Compile Include="Model\OrderDetailModel.cs" />
    <Compile Include="Model\OrderLogisticModel .cs" />
    <Compile Include="Model\ExcelColumnModel.cs" />
    <Compile Include="Model\OrderInfoModel.cs" />
    <Compile Include="Model\Payment_Detail.cs" />
    <Compile Include="Model\RegisterCheckInBasicModel.cs" />
    <Compile Include="Model\RegisterCheckInDetailModel.cs" />
    <Compile Include="Model\RegisterReportModel.cs" />
    <Compile Include="Model\SmsContentANCBModel.cs" />
    <Compile Include="Model\SubModelRefundTransaction.cs" />
    <Compile Include="Model\CustomsDeclarationSetModel.cs" />
    <Compile Include="Model\CAPRModel.cs" />
    <Compile Include="Model\ContInfoOtmModel.cs" />
    <Compile Include="Model\Customs39Model.cs" />
    <Compile Include="Model\DVCBModel.cs" />
    <Compile Include="Model\EInvoice.cs" />
    <Compile Include="Model\EirModel.cs" />
    <Compile Include="Model\DVTBModel.cs" />
    <Compile Include="Model\GTHAModel.cs" />
    <Compile Include="Model\HBCXModel.cs" />
    <Compile Include="Model\HouseBillModel.cs" />
    <Compile Include="Model\InvoiceDetailModel.cs" />
    <Compile Include="Model\InvoiceModel.cs" />
    <Compile Include="Model\NHARModel.cs" />
    <Compile Include="Model\VesselVoyageSearchConfigModel.cs" />
    <Compile Include="Model\ViewCustoms39Container.cs" />
    <Compile Include="Model\WebMessageModel.cs" />
    <Compile Include="Model\YardOfServiceModel.cs" />
    <Compile Include="MSDS_FILE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="OrderContext.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>OrderContext.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="OrderContext.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="OrderContext.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>OrderContext.edmx</DependentUpon>
    </Compile>
    <Compile Include="OrderContext1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>OrderContext.edmx</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_CHECK_IN.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_COMMODITY.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_CONT_MOVEMENT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_DOCUMENT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_EBOOKING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_IMO.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_LOGISTIC.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_MSDS_MAP.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_OOG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_OTP.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_PORT_CHANGE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_PROPERTY.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_SERVICE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_TEMP.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_DETAIL_TRUCK.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_LOGISTIC.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="ORDER_TRUCK.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PROVINCE.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_AVAILBLE_ORDER_DETAILS_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_CHARGES_BY_SERIAL_INVOICE_NO_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_CHARGE_INFORMATION_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_CONTAINERS_SHIPPING_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_CUSTTAX_BY_ORDERDETAILNO_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_BARGE_LIST_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_CHARGE_LIST_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_INVOICE_EXACT_INTF_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_INVOICE_REPORT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_INVOICE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_LOYALTY_INVOICE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_EPORT_PAYMENT_REPORT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_INVOICES_BY_ORDERDETAILNO_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_INVOICES_BY_ORDER_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_INVOICES_INFO_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_INVOICE_DETAIL_REVENUE_REPORT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_INVOICE_ERROR_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_INVOICE_REVENUE_REPORT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_ITEMNOS_BY_ORDERIDS_FORMITEMNOS_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_LIST_CONT_REGISTED_CARGO_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_LIST_CONT_REGISTED_DECL_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_ORDERS_LIST_BY_METHOD_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_ORDER_DETAILS_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_ORDER_DETAIL_CHARGE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_ORDER_DETAIL_PAID_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_PAYMENT_DETAIL_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_PAYMENT_LIST_LOGIN_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_PAYMENT_LIST_NOT_LOGIN_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_PORT_CHANGE_WITHOUT_APPLY_CHANGE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REGISTER_DEBIT_LIST_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REGISTER_DEBIT_ONLINE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPLACE_INVOICE_DETAIL_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPORT_CHECKIN_ONLINE_BY_CREATED_DATE_V4_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPORT_CHECKIN_ONLINE_BY_ESTIMATED_TIME_V4_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPORT_CHECKIN_ONLINE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_REPORT_CHECKIN_ONLINE_V4_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_SERVICE_ATTACH_EXEC_ADMIN_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_SERVICE_ATTACH_EXEC_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_SERVICE_ATTACH_EXEC_USER_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTIONS_NOT_PUBLISH_INVOICE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_BY_ORDER_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_ERROR_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_INFO_TO_PUBLISH_INV_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_INVOICE_PAYMENT_ADMIN_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_ADMIN_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_INVOICE_PAYMENT_BYINVDATE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_INVOICE_PAYMENT_EXEC_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_INVOICE_PAYMENT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_PAYMENT_DEBIT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_REFUND_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_REPORT_ADMIN1_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_REPORT_ADMIN_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_REPORT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_REPORT_USER1_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_REPORT_USER_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSACTION_TO_COMPARE_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_TRANSFER_PAYMENT_REPORT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="PR_GET_USER_FEATURE_LIST_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="SERVICE_CONFIG.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="SP_GET_OPER_METHOD_CONFIG_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="sp_helpdiagramdefinition_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="sp_helpdiagrams_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="SP_ORDER_DETAIL_PORT_CHANGE_FILTER_BY_AGENT_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="SP_ORDER_DETAIL_PORT_CHANGE_FILTER_Result.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="sysdiagrams.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="TRANSACTION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="TRANSACTION_DEBIT_COMPLETED.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="TRANSPORT.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="TRANSPORT_DETAIL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="TRUCK.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="TRUCK_TRACKING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="USER_ACTIVATION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="USER_CONNECTION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="VESSEL_VOYAGE_PORT_CHANGE_SERVICES.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="VE_ICD_CONT_INFO.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="VE_OPER_METHOD_SETTING.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="VE_ORDER_DETAIL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="VIEW_VES_VOYAGES_LLPOD_CHANGE_DETAIL.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="VIEW_ORDER_DETAIL_PORT_CHANGE_INFORMATION.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
    <Compile Include="WARD.cs">
      <DependentUpon>OrderContext.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <EntityDeploy Include="OrderContext.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>OrderContext1.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Compile Include="EntityPartial\AM_USER_LINER.Partial.cs" />
    <Compile Include="EntityPartial\CHECK_IN_ITEM.Partial.cs" />
    <Compile Include="EntityPartial\AM_USER_AGENT.Partial.cs" />
    <Compile Include="EntityPartial\PROVINCE.Partial.cs" />
    <Compile Include="EntityPartial\USER_CONNECTION.Partial.cs" />
    <Compile Include="EntityPartial\PR_GET_TRANSACTION_INVOICE_PAYMENT_EXEC_Result.Partial.cs" />
    <None Include="OrderContext.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>OrderContext.edmx</DependentUpon>
      <LastGenOutput>OrderContext.Context.cs</LastGenOutput>
    </None>
    <None Include="OrderContext.edmx.diagram">
      <DependentUpon>OrderContext.edmx</DependentUpon>
    </None>
    <None Include="OrderContext.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>OrderContext.edmx</DependentUpon>
      <LastGenOutput>OrderContext.cs</LastGenOutput>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Snp.ePort.Entity.ruleset" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Snp.ePort.Common\Snp.ePort.Common.csproj">
      <Project>{f58832de-5553-42c9-8566-8120535248a8}</Project>
      <Name>Snp.ePort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Tos.Entity\Snp.Tos.Entity.csproj">
      <Project>{62D4BFE6-7DE9-44EC-A16D-F15A2A894235}</Project>
      <Name>Snp.Tos.Entity</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Analyzer Include="..\..\packages\Microsoft.CodeAnalysis.BannedApiAnalyzers.2.9.3\analyzers\dotnet\cs\Microsoft.CodeAnalysis.BannedApiAnalyzers.dll" />
    <Analyzer Include="..\..\packages\Microsoft.CodeAnalysis.BannedApiAnalyzers.2.9.3\analyzers\dotnet\cs\Microsoft.CodeAnalysis.CSharp.BannedApiAnalyzers.dll" />
    <Analyzer Include="..\..\packages\Microsoft.CodeAnalysis.PublicApiAnalyzers.2.9.3\analyzers\dotnet\Microsoft.CodeAnalysis.PublicApiAnalyzers.CodeFixes.dll" />
    <Analyzer Include="..\..\packages\Microsoft.CodeAnalysis.PublicApiAnalyzers.2.9.3\analyzers\dotnet\Microsoft.CodeAnalysis.PublicApiAnalyzers.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Microsoft.CodeAnalysis.BannedApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.BannedApiAnalyzers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Microsoft.CodeAnalysis.BannedApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.BannedApiAnalyzers.props'))" />
    <Error Condition="!Exists('..\..\packages\Microsoft.CodeAnalysis.PublicApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.PublicApiAnalyzers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Microsoft.CodeAnalysis.PublicApiAnalyzers.2.9.3\build\Microsoft.CodeAnalysis.PublicApiAnalyzers.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>