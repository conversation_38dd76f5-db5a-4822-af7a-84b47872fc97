﻿let itemDetail = {};
let isSubmit = false;
var mainForm = 'frm-full-container-receiving';
var serviceRegisterForm = 'frm-register-form';
var billRegisterForm = 'frm-register-bill-form';
var approvalForm = 'frm-approval-form';
var needSubmitForm = false;
var HasEDO = false;
const _MS_PER_DAY = 1000 * 60 * 60 * 24;
var alpha = /[ A-Za-z]/;
var numeric = /[0-9]/;
var numericAndDot = /[0-9\.]/;
var alphanumeric = /[ A-Za-z0-9\-]/;
var sealNoInput = /[A-Za-z0-9\~!@#$%^&*()\-[\]{}";:|/\\|,.+=|\/_]/;
var notSealNoInput = /[^A-Za-z0-9\~!@#$%^&*()\-[\]{}";:|/\\|,.+=|\/_]/;
var notAlphanumeric = /[^ A-Za-z0-9]/;
var phone = /[0-9]/;
var regExSpecial = /[ A-Za-z0-9\-|\/_]/;
var OperType = "";
var g_OrderID = 0;
var isEdit = false;
var isContEdit = false;
var isSearchFromContainerNo = 0;
var isFirstShowTransport = false;
var isClosePopup = false;
var cateId = 0;
var hotline = '';
var lstService = null;
var registerCode = '';
var siteName = '';
var mainScreen = {};
var itemEdit = '';
var isResetService = false;
var tmp;
let isValidCustom = false;
let vesselLocked = true;
// Popup Thông tin nhiệt độ
var TempArray = {
    // containerNo : '',
    temp: '',
    ventilation: '',
    unit: '',
    humidity: '',
    chkTemp: false,
};

// Giá trị ISo khi popup Phiếu đăng ký hiện lên.
var isoDefault = "";

// Thông tin quá khổ - Của Popup quá khổ
var overSizeArrayOfPopup = {
    top: 0,
    right: 0,
    left: 0,
    behind: 0,
    front: 0,
    length: 0,
    height: 0,
    width: 0,
    noOverSize: false,
}

// Popup Thông tin quá khổ
var overSizeArrayDefault = {
    top: 0,
    right: 0,
    left: 0,
    behind: 0,
    front: 0,
    length: 0,
    height: 0,
    width: 0,
    noOverSize: false,
};

var SpecialService = [];
var leftItems = new Array();
var rightItems = new Array();

var dsTransportTypes = '';
var dsVesselList = '';
var dsLineopers = '';
var dsIsoes = '';
var dsPlaceOfReceipts = '';
var dsImos = '';
var dsVesselVoyages = '';
var dsPortToByVesssels = '';
var dsExtraPhones = '';
var dsPhoneAntiCompany = '';
var dsServiceInspecteds = '';
var row = null;
var asynchronize = false;
var unnoImoList = new Array();
//data source của grid
var listImoUnnoOrderDetail = new Array();
var detailPaymentStatus = false;
//cờ check tạo phiếu đăng ký
var createFlag = false;

$(document).ready(function () {
    mainScreen = {
        txtMalo: Factory.CreateTextBox('#txtOrderId'),
        txtMaSoThue: Factory.CreateTextBox('#txtTaxNo'),
        nbxSoLuongContainer: Factory.CreateNumberBox('#nbxSoLuongContainer'),
        txtGhiChu: Factory.CreateTextBox('#txtGhiChu'),
        txtOperType: Factory.CreateTextBox('#txtOperType'),
        lblInfoCustomer: $('#lblInfoCustomer'),

        //Pop up Special Stacking
        listService: Factory.CreateList('#listService'),
        selectedService: Factory.CreateList('#selectedService'),
        btnMoveAllItemsToRight: Factory.CreateButton('#btn-add-All'),
        btnMoveAllItemsToLeft: Factory.CreateButton('#btn-del-All'),
        btnMoveSelectedItemsToRight: Factory.CreateButton('#btn-add'),
        btnMoveSelectedItemsToLeft: Factory.CreateButton('#btn-del'),

        //Order detail
        gridOrderDetails: Factory.CreateDataGrid('#lstOrderDetails'),
        gridErrorPopupBookNoFpod: Factory.CreateDataGrid('#list-error-popup-bookno-fpod'),
        gridContainerDangerous: Factory.CreateDataGrid('#lstContainerDangerous'),

        //Pop up
        popupChoiceOrder: Factory.CreatePopup('#popup-batch-no-list'),
        //Pop up - Create
        popupRegister: Factory.CreatePopup('#popup-create'),
        popupImport: Factory.CreatePopup('#popup-import'),
        popupProductInfor: Factory.CreatePopup('#popup-portduct-infor'),
        popupRegisterService: Factory.CreatePopup('#popup-register-service'),
        popupSpecialStacking: Factory.CreatePopup('#popup-special-stacking'),
        popupTemperatureInfor: Factory.CreatePopup('#popup-temperature-information'),
        popupOversizeInfor: Factory.CreatePopup('#popup-oversize-information'),
        popupImoUnnoInfo: Factory.CreatePopup('#popup-imo-unno-info'),
        popupReport: Factory.CreatePopup('#popup-report'),
        popupStopCont: Factory.CreatePopup('#stop-cont-popup'),
        popupChangeBookNoFpod: Factory.CreatePopup('#popup-change-bookno-fpod'),
        popupErrorChangeBookFpd: Factory.CreatePopup('#popup-error-change-popup-book-fpod-info'),

        // Control popup register/edit
        txtRegisterID: Factory.CreateTextBox('#txtRegisterID'),
        txtOrderDetailId: Factory.CreateTextBox('#txtOrderDetailId'),
        txtOrderDetailNo: Factory.CreateTextBox('#txtOrderDetailNo'),
        txtDelivery: Factory.CreateTextBox('#txtDelivery'),
        dtxCreateDate: Factory.CreateDateBox('#dtxCreateDate'),
        slxOperMethod: Factory.CreateSelectBox('#slxOperMethod'),
        slxAgent: Factory.CreateSelectBox('#slxAgent'),
        txtBookNo: Factory.CreateTextBox('#txtBookNo'),
        slxVessel: Factory.CreateSelectBox('#slxVessel'),
        slxPortFrom: Factory.CreateSelectBox('#slxPortFrom'),
        slxPortTo: Factory.CreateSelectBox('#slxPortTo'),
        txtContainerNo: Factory.CreateTextBox('#txtContainerNo'),
        txtSealNo: Factory.CreateTextBox('#txtSealNo'),
        slxIso: Factory.CreateSelectBox('#slxIso'),
        numGrossWeight: Factory.CreateNumberBox('#numGrossWeight'),
        chkVGM: Factory.CreateCheckBox('#chkVGM'),
        txtCertifiedPlace: Factory.CreateTextBox('#txtCertifiedPlace'),
        numMaxGross: Factory.CreateNumberBox('#numMaxGross'),
        slxTransportType: Factory.CreateSelectBox('#slxTransportType'),
        slxVoyageBarge: Factory.CreateSelectBox('#slxVoyageBarge'),
        slxDepot: Factory.CreateSelectBox('#slxDepot'),
        gridImoUnno: Factory.CreateDataGrid('#gridImoUnno'),
        txtUNNO: Factory.CreateTextBox('#txtUNNO'),
        slxMobilePhone: Factory.CreateSelectBox('#slxMobilePhone'),
        txtRemark: Factory.CreateTextArea('#txtRemark'),
        chkConfirm: Factory.CreateCheckBox('#chkConfirm'),
        // Popup MSDS
        popupMSDS: Factory.CreatePopup('#popup-import-MSDS'),
        // storage Id để lưu thông tin kiểm hóa, khử trùng
        txtStorageId: Factory.CreateTextBox('#txtStorageId'),
        txtNewBookNo: Factory.CreateTextBox('#txtNewBookNo'),

        btnOverSize: Factory.CreateButton('#btnOverSize'),
        btnReeferCont: Factory.CreateButton('#btnReeferCont'),
        btnInfoProduct: Factory.CreateButton('#btnInfoProduct'),
        btnSpecialStacke: Factory.CreateButton('#btnSpecialStacke'),
        btnService: Factory.CreateButton('#btnService'),
        btnSaveOrderDetail: Factory.CreateButton('#btnSaveOrderDetail'),
        btnPayment: Factory.CreateButton('#btn-payment'),
        btnEditBookNoFpod: Factory.CreateButton('#btnEditBookNoFpod'),
        btnCloseErrorPopup: Factory.CreateButton('#btnCloseErrorPopupCustoms'),
        // Control popup register service

        rdxService_Inspected: Factory.CreateRadioGroup('#rdxInspected'),
        chkSevice_Antiseptic: Factory.CreateCheckBox('#chkAntiseptic'),
        slxService_PhoneNumberAntiDepartment: Factory.CreateSelectBox('#slxPhoneNumberAntiDepartment'),
        slxNewPortTo: Factory.CreateSelectBox('#slxNewPortTo'),

        // Control popup register product info
        txtProd_ContainerNo: Factory.CreateTextBox('#txtProd_ContainerNo'),
        txtProd_IMO: Factory.CreateTextBox('#txtProd_IMO'),
        txtProd_UNNO: Factory.CreateTextBox('#txtProd_UNNO'),
        txtProd_Name: Factory.CreateTextBox('#txtProd_Name'),
        txtProd_Pack: Factory.CreateTextBox('#txtProd_Pack'),
        txtProd_OwnName: Factory.CreateTextBox('#txtProd_OwnName'),

        // Control popup temperature information
        txtTemp_ContainerNo: Factory.CreateTextBox('#txtTemp_ContainerNo'),
        numTemp_Temperature: Factory.CreateNumberBox('#numTemp_Temperature'),
        txtTemp_Ventilation: Factory.CreateTextBox('#txtTemp_Ventilation'),
        slxTemp_Unit: Factory.CreateSelectBox('#slxTemp_Unit'),
        numTemp_Humidity: Factory.CreateNumberBox('#numTemp_Humidity'),
        chkTemp_Temperature: Factory.CreateCheckBox('#chkTemp_Temperature'),

        // Control popup OverSize information
        txtOversize_ContainerNo: Factory.CreateTextBox('#txtOversize_ContainerNo'),
        txtOversize_ISO: Factory.CreateTextBox('#txtOversize_ISO'),
        txtOversize_Unit: Factory.CreateTextBox('#txtOversize_Unit'),
        chkOversize_OW: Factory.CreateCheckBox('#chkOversize_OW'),
        chkOversize_OH: Factory.CreateCheckBox('#chkOversize_OH'),
        chkOversize_OL: Factory.CreateCheckBox('#chkOversize_OL'),
        numOversize_Top: Factory.CreateNumberBox('#numOversize_Top'),
        numOversize_Right: Factory.CreateNumberBox('#numOversize_Right'),
        numOversize_Left: Factory.CreateNumberBox('#numOversize_Left'),
        numOversize_Behind: Factory.CreateNumberBox('#numOversize_Behind'),
        numOversize_Front: Factory.CreateNumberBox('#numOversize_Front'),
        numOversize_Length: Factory.CreateNumberBox('#numOversize_Length'),
        numOversize_Height: Factory.CreateNumberBox('#numOversize_Height'),
        numOversize_Width: Factory.CreateNumberBox('#numOversize_Width'),
        chkOversize_NoOversize: Factory.CreateCheckBox('#chkOversize_NoOversize'),

        popupCommodityInfo: Factory.CreatePopup('#popup-commodity-info'),

        listErrorGrid: Factory.CreateDataGrid('#list-error-popup'),
        popupListErrorPopup: Factory.CreatePopup('#popup-list-error-popup'),
    };
    //pop up thêm imo unno
    if (ApplyImo == 'Y') {
        mainScreen.txtIMO = Factory.CreateTextBox('#txtIMO');
        mainScreen.slxImopopup = Factory.CreateSelectBox('#slxIMOPopup');
        mainScreen.slxUnno = Factory.CreateSelectBox('#slxUnno');
        mainScreen.btnXoaImo = Factory.CreateButton('#btn-xoa-imo');
        mainScreen.btnDongImo = Factory.CreateButton('#btn-dong-imo');
        mainScreen.btnThemImo = Factory.CreateButton('#btn-them-imo');
    } else {
        mainScreen.slxIMO = Factory.CreateSelectBox('#slxIMO');
    }

    init();

    if (OrderId != 0) {
        mainScreen.txtMalo.option(opt.value, OrderId);
        loadData(OrderId)
    }

    //TOS-2854
    $('#nbxSoLuongContainer').keypress(function (e) {
        var keycode = (e.keyCode ? e.keyCode : e.which);
        if (keycode == '13') {
            e.preventDefault();
            $('#btnSaveOrder').focus();
            setTimeout(function () {
                onSaveOrder();
            }, 300);
        }
    });

    // bug tos-3325
    var phoneNumber = Factory.CreateSelectBox('#slxPhoneNumberAntiDepartment');
    if (!phoneNumber.option('visible')) {
        $('#lbAntiseptic').hide();
    }
    
});

function init() {
    if (userRole === 'ICD')
        mainScreen.btnPayment.option('disabled', true);
    else
        mainScreen.btnPayment.option('disabled', false);
}

var showPopupChoiceOrder = false;
// Chọn lô Hàng
function onPopupChoiceOrder(e) {
    if (showPopupChoiceOrder) {
        getPopupInstanceBatchNoList().show();
    } else {
        var operType = mainScreen.txtOperType.option(devExtremeOptions.value);
        onDisplayPopupBatchNoList(operType, false, false);
        showPopupChoiceOrder = true;
    }
}
function onPopupChoiceOrderHiding(e) {
    var orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    if (orderId != g_OrderID && g_OrderID > 0) {
        if (g_OrderID && g_OrderID.length > 0) {
            mainScreen.txtMalo.option('value', g_OrderID);
            loadData(g_OrderID);
        } else {
            if (orderId > 0)
                loadData(orderId);
            else
                onNewOrder();
        }
    }
}
// ---------
function onNewOrder() {
    mainScreen.txtMalo.option(devExtremeOptions.value, null);
    mainScreen.txtMaSoThue.option(devExtremeOptions.value, null);
    mainScreen.txtMaSoThue.focus();
    mainScreen.nbxSoLuongContainer.option(devExtremeOptions.value, 1);
    mainScreen.lblInfoCustomer.text(null);
    mainScreen.txtGhiChu.option(devExtremeOptions.value, '');

    // reset lại GirdView
    mainScreen.gridOrderDetails.option({
        dataSource: null
    });

    setDisableControl(null);
}

function onSaveOrder() {
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    let cusTaxNo = isNull(mainScreen.txtMaSoThue.option(devExtremeOptions.value));
    let quantity = mainScreen.nbxSoLuongContainer.option(devExtremeOptions.value);
    let remark = mainScreen.txtGhiChu.option(devExtremeOptions.value);
    // Check số lượng cont
    if (quantity <= 0) {
        message.error("Vui lòng nhập đầy đủ các thông tin MST và số lượng container phải lớn hơn 0.");
        mainScreen.nbxSoLuongContainer.option(devExtremeOptions.value, 1);
        return;
    }
    // Lấy link xữ lý theo phương án
    var operType = mainScreen.txtOperType.option(devExtremeOptions.value);
    var linkFunction = GetLinkFunction(operType);
    if (linkFunction == "") {
        message.error("Không tìm thấy Phương án");
        return;
    }
    //------
    let data = {
        'OrderId': orderId,
        'CusTaxNo': trimEx(cusTaxNo).toUpperCase(),
        'Quantity': quantity,
        'Remark': remark
    };

    return httpClient.post(linkFunction, { 'model': data })
        .done((res) => {
            try {
                if (res) {
                    if (res.type === messageType.error) {
                        message.error(res.content);
                    } else {
                        if (res.model.OrderDetailsList !== 'undefine')
                            res.model.OrderDetailsList.formatDate('CREATED_DATE');

                        const dataSource = {
                            store: {
                                type: 'array',
                                key: 'ORDER_DETAIL_ID',
                                data: res.model.OrderDetailsList
                            }
                        };
                        mainScreen.gridOrderDetails.option({
                            dataSource: dataSource
                        });
                        mainScreen.gridOrderDetails.clearFilter("row");

                        mainScreen.txtMalo.option(devExtremeOptions.value, res.model.ORDER_ID);
                        mainScreen.nbxSoLuongContainer.option(devExtremeOptions.value, res.model.QUANTITY);
                    }
                }
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        });
}

function getDataRegister() {
    const registerId = mainScreen.txtRegisterID.option(opt.value);
    const orderDetailId = mainScreen.txtOrderDetailId.option(opt.value);
    const orderDetailNo = mainScreen.txtOrderDetailNo.option(opt.value);
    const createDate = mainScreen.dtxCreateDate.option(opt.value);
    var operMethod = mainScreen.slxOperMethod.option(opt.value);
    const agentLineOper = mainScreen.slxAgent.option(opt.value);
    const bookNo = mainScreen.txtBookNo.option(opt.value);
    const vessel = mainScreen.slxVessel.option(opt.value);
    var portFrom = mainScreen.slxPortFrom.option(opt.value);
    const portTo = mainScreen.slxPortTo.option(opt.value);
    const containerNo = mainScreen.txtContainerNo.option(opt.value);
    const sealNo = mainScreen.txtSealNo.option(devExtremeOptions.value);
    const iso = mainScreen.slxIso.option(opt.value);
    const grossWeight = mainScreen.numGrossWeight.option(opt.value);
    const VGM = mainScreen.chkVGM.option(opt.value);
    const certifiedPlace = mainScreen.txtCertifiedPlace.option(opt.value);
    const maxGross = mainScreen.numMaxGross.option(opt.value);
    const transportType = mainScreen.slxTransportType.option(opt.value);
    var voyageBarge = mainScreen.slxVoyageBarge.option(opt.value);
    const depot = mainScreen.slxDepot.option(opt.value);
    let IMO = '';
    if (ApplyImo == 'Y') {
        IMO = mainScreen.txtIMO.option(opt.value);
    } else {
        IMO = mainScreen.slxIMO.option(opt.value);
    }
    const UNNO = mainScreen.txtUNNO.option(opt.value);
    const mobilePhone = mainScreen.slxMobilePhone.option(opt.value);
    const remark = mainScreen.txtRemark.option(opt.value);
    const confirm = mainScreen.chkConfirm.option(opt.value);
    const orderId = mainScreen.txtMalo.option(devExtremeOptions.value);

    //  Lấy thông hàng hóa

    const hazardousContent = mainScreen.txtProd_Name.option(devExtremeOptions.value);
    const packageType = mainScreen.txtProd_Pack.option(devExtremeOptions.value);
    const emergencyPhone = mainScreen.txtProd_OwnName.option(devExtremeOptions.value);

    // Lấy thông tin nhiệt độ
    const vent = mainScreen.txtTemp_Ventilation.option(devExtremeOptions.value);
    const ventUnit = mainScreen.slxTemp_Unit.option(devExtremeOptions.value);
    const temp = mainScreen.numTemp_Temperature.option(devExtremeOptions.value);
    const tempHumidity = mainScreen.numTemp_Humidity.option(devExtremeOptions.value);
    const noPluginRequired = mainScreen.chkTemp_Temperature.option(opt.value);

    // Lấy thông tin quá khổ

    var lineOper = agentLineOper ? agentLineOper.split('|')[1] : '';
    var agent = agentLineOper.split('|')[0];

    if (mainScreen.slxPortFrom.option('text') == null || mainScreen.slxPortFrom.option('text') == '')
        portFrom = '';

    var operName = mainScreen.slxOperMethod.option('text');
    if (!operName)
        operMethod = '';
    voyageBarge = trimEx(voyageBarge) == 'null' ? '' : trimEx(voyageBarge);


    return {
        ORDER_ID: orderId,
        ORDER_DETAIL_ID: orderDetailId,
        ORDER_DETAIL_NO: orderDetailNo,
        CREATED_DATE: createDate,
        TRANSPORT_TYPE: transportType,
        VOYAGE_BARGE: voyageBarge,
        OPER_METHOD: operMethod,
        ITEM_NO: containerNo ? trimEx(containerNo).toUpperCase() : '',
        AGENT: agent,
        LINE_OPER: lineOper,
        BOOKING_NO: bookNo ? trimEx(bookNo.toUpperCase()) : '',
        VES_ID: vessel,
        ISO_CODE: iso,
        GROSS_WEIGHT: grossWeight,
        SEAL_NO: sealNo ? trimEx(sealNo.toUpperCase()) : '',
        REMARK: remark,
        LLPOD: portFrom,
        FPOD: portTo,
        MAX_GROSS: maxGross,
        HAZADOUS: IMO,
        PLACE_OF_RECEIPT: depot,
        UNNO: UNNO,
        PHONE_NUMBER: mobilePhone,
        HAZARDOUS_CONTENT: hazardousContent,
        PACKAGE_TYPE: packageType,
        EMERGENCY_PHONE: emergencyPhone,
        WEIGHT_CERTIFIED: VGM === false ? 0 : 1,
        CERTIFIED_PLACE: certifiedPlace,
        VENT: vent,
        VENT_UNIT: ventUnit,
        HUMIDITY: tempHumidity,
        TEMP: temp,
        NO_PLUGIN_REQUIRED: noPluginRequired ? 'Y' : 'N'
    };
}

//validate declare product info

function validateProductInfo() {
    let hazardous = '';
    if (ApplyImo == 'Y') {
        hazardous = mainScreen.txtIMO.option(opt.value);
    } else {
        hazardous = mainScreen.slxIMO.option(opt.value);
    }

    const unno = mainScreen.txtUNNO.option(opt.value);
    //  Lấy thông hàng hóa
    var hazardousContent = mainScreen.txtProd_Name.option(devExtremeOptions.value);
    var packageType = mainScreen.txtProd_Pack.option(devExtremeOptions.value);
    var emergencyPhone = mainScreen.txtProd_OwnName.option(devExtremeOptions.value);

    var errorMsg = '';
    if (isDeclareProductInfo == 'True') {
        if (hazardousContent == '' || emergencyPhone == '')
            errorMsg = 'Vui lòng khai báo đầy đủ thông tin hàng hóa<br>';
        else if ((isNull(hazardous) != '' || unno != '') && packageType == '')
            errorMsg += 'Vui lòng khai báo đầy đủ thông tin hàng hóa.<br>';

        if (hazardousContent.length > 256)
            errorMsg += 'Vui lòng nhập Tên hàng hóa/ hoạt chất không vượt quá 256 ký tự.<br>';

        if (packageType.length > 50)
            errorMsg += 'Vui lòng nhập Quy cách đóng gói không vượt quá 50 ký tự.<br>';

        if (emergencyPhone.length > 120)
            errorMsg += 'Vui lòng nhập Chủ hàng (Tên/ Điện thoại) không vượt quá 120 ký tự.';
    }
    if (errorMsg) {
        onPopupProductInfor();
        message.error(errorMsg);
        return false;
    }
    return true;
}

// Lấy thông tin dịch vụ kiểm hóa khử trùng
function getDataRegisterServicce(storageId) {
    var name = storageId;
    var services = localStorage.getItem(name);

    if (isEmpty(services)) {
        return null;
    }

    return services;
}

function validateServicce() {
    const isKhuTrung = mainScreen.chkSevice_Antiseptic.option(devExtremeOptions.value);
    const phonNumber = mainScreen.slxService_PhoneNumberAntiDepartment.option(devExtremeOptions.value);
    if (isKhuTrung && phonNumber == '') {
        return 'Vui lòng nhập Số điện thoại đơn vị Khử trùng!';
    }
    return '';
}

function validateOverSize() {
    let iso = mainScreen.slxIso.option(devExtremeOptions.value);
    var chkOOG = mainScreen.chkOversize_NoOversize.option(devExtremeOptions.value);
    var chkOogOH = mainScreen.chkOversize_OH.option(opt.value);
    var chkOogOL = mainScreen.chkOversize_OL.option(opt.value);
    var chkOogOW = mainScreen.chkOversize_OW.option(opt.value);

    if (iso.substr(-2, 2) != '50' && iso.substr(-2, 2) != '60') {
        return true;
    }

    return !chkOOG ? chkOogOW || chkOogOH || chkOogOL : true;
}

// Lấy thông tin dịch vụ xếp dỡ đặc biệt
function getDataRegisterSpecialStacking() {
    let services = [];
    var listSelected = mainScreen.selectedService.option('items');
    if (listSelected != "") {
        for (var i = 0; i < listSelected.length; i++) {
            services.push(listSelected[i].CODE);
        }
    }
    return services;
}

function saveOrderDetail() {
    var errorMessage = checkConfirmStatus();
    if (errorMessage !== '') {
        message.error(errorMessage);
        return;
    }

    beforeSave();
}

function getDataOverSize() {
    const oog_Behind = mainScreen.numOversize_Behind.option(opt.value);
    const oog_Front = mainScreen.numOversize_Front.option(opt.value);
    const oog_Height = mainScreen.numOversize_Height.option(opt.value);
    const oog_Left = mainScreen.numOversize_Left.option(opt.value);
    const oog_Right = mainScreen.numOversize_Right.option(opt.value);
    const oog_Length = mainScreen.numOversize_Length.option(opt.value);
    const oog_Width = mainScreen.numOversize_Width.option(opt.value);
    const oog_Top = mainScreen.numOversize_Top.option(opt.value);
    const oog_Flg = mainScreen.chkOversize_NoOversize.option(devExtremeOptions.value);

    return {
        OOG_BEHIND: oog_Behind,
        OOG_FRONT: oog_Front,
        OOG_HEIGHT: oog_Height,
        OOG_LEFT: oog_Left,
        OOG_RIGHT: oog_Right,
        OOG_LENGTH: oog_Length,
        OOG_WIDTH: oog_Width,
        OOG_TOP: oog_Top,
        OOG_WIDTH_FLG: oog_Width > 0 ? 1 : 0,
        OOG_HEIGTH_FLG: oog_Height > 0 ? 1 : 0,
        OOG_LENGHT_FLG: oog_Length > 0 ? 1 : 0,
        OOG_FLG: oog_Flg,
        OOG_ISO_TYPE: 'Y',
        UNIT: 'C',
    };
}

function beforeSave() {
    var data = getDataRegister();

    //Check validate Input
    let iso = mainScreen.slxIso.option(devExtremeOptions.value);
    if (!iso || iso == '') {
        message.error("Chưa điền thông tin kích cỡ");
        return;
    }

    //Khai bao thong tin hang hoa
    if (isUserNewVerComdDecl !== 'Y') {
        if (!validateProductInfo())
            return;
    }

    //Dich vu kiem hoa
    var errorValidateService = validateServicce();
    if (errorValidateService != '') {
        message.error(errorValidateService);
        return;
    }

    //Hang qua kho
    var errorOverSize = validateOverSize();
    if (!errorOverSize) {
        onPopupOversizeInfor();
        message.error("Chưa cập nhật thông tin quá khổ");
        return;
    }

    httpClient.post('/FullContainerDelivery/CheckBookingRuleAndISO', { request: data })
        .done(res => {
            if (res) {
                if (res.type === messageType.success) {
                    if (res.content == '')
                        onSave();
                    else {
                        onConfirmBeforeSave(res.content);
                    }
                }
                else {
                    message.error(res.content);
                }
            }
        }).fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        });
}

function onConfirmBeforeSave(msg) {
    $.confirm({
        title: 'Xác nhận !',
        content: msg,
        type: 'orange',
        typeAnimated: true,
        draggable: true,
        animationSpeed: 200,
        animation: 'zoom',
        closeAnimation: 'scale',
        buttons: {
            confirm: {
                text: 'XÁC NHẬN',
                btnClass: 'btn-red',
                action: function () {
                    onSave();
                }
            },
            cancel: {
                text: 'Hủy',
                btnClass: 'btn-dark',
                action: function () {
                }
            }
        }
    });
}

function onSave() {
    var data = getDataRegister();

    //TOS-6093
    let storage = mainScreen.txtStorageId.option(devExtremeOptions.value);
    var temp_services = getDataRegisterServicce(storage);
    var isChangeService = !isEmpty(temp_services);
    var services = JSON.parse(temp_services);
    if (!isChangeService)
        services = ["", "", ""];
    else {
        if (services[2] == true && services[1] == null) {
            message.error("Vui lòng nhập Số điện thoại đơn vị Khử trùng!");
            return;
        }
    }
    var propertyCodes = getDataRegisterSpecialStacking();
    var overSizeInfo = getDataOverSize();

    if (getVesselLocked())
        return;

    let commodities = mainScreenPopup.gridCommodityDetail.getDataSource()?._items;

    const saveOrderDetail = function () {
        var url = '/FullContainerDelivery/OnCreate';
        if (isEdit === true)
            url = '/FullContainerDelivery/OnEdit';

        const input = {
            request: data,
            services: services,
            propertyCodes: propertyCodes,
            orderDetailOOG: overSizeInfo,
            isChangeService: isChangeService,
            commodities: commodities
        };
        httpClient.post(url, input)
            .done(res => {
                if (res) {
                    if (res.type === messageType.success) {
                        message.success(res.content);
                        isSubmit = true;
                        onEdit(data.ORDER_DETAIL_NO, false);

                        localStorage.removeItem(storage);
                        //lưu thành công gen storageId mới
                        storageId = GenStorageId();
                        mainScreen.txtStorageId.option(opt.value, storageId);

                        // reset popup quá khổ
                        resetOverSize();
                    }
                    else {
                        message.error(res.content);
                    }
                }
            }).fail((jqXhr, textStatus, errorThrow) => {
                message.error(textStatus + ': ' + errorThrow);
            });
    }


    const checkApplyCommodity = function (isApplyCommodity) {
        // nếu có cấu hình hiển thị popup thông báo xác nhận dk giao lô hàng
        if (isApplyCommodity && isApplyCommodity == 'Y') {
            const operMethod = mainScreen.slxOperMethod.option("dataSource.store.data").find(function (value) {
                return value.Key == mainScreen.slxOperMethod.option(opt.value);
            });

            if (!operMethod || !operMethod.Extend) {
                message.error("Phương án " + mainScreen.slxOperMethod.option(opt.value) + " không tồn tại trong hệ thống.");
                return;
            }

            const transportType = mainScreen.slxTransportType.option("dataSource").find(function (value) {
                return value.ID == mainScreen.slxTransportType.option(opt.value);
            });

            if (transportType == null || transportType === undefined) {
                message.error("Vui lòng nhập đầy đủ thông tin Phương tiện vận chuyển.");
                return;
            }

            const msgConfirm = '<p style="color: #337ab7;">Bạn muốn ghi nhận thông tin đăng ký giao lô <b>' + operMethod.Extend
                + '</b> cho cảng bằng phương tiện <b>' + transportType.TRANSPORT_TYPE + '</b></p>'

            $.confirm({
                title: 'XÁC NHẬN',
                content: msgConfirm,
                type: 'orange',
                typeAnimated: true,
                draggable: true,
                animationSpeed: 300,
                animation: 'zoom',
                closeAnimation: 'scale',
                buttons: {
                    confirm: {
                        text: 'Xác nhận',
                        btnClass: 'btn-red',
                        action: function () {
                            saveOrderDetail();
                        }
                    },
                    cancel: {
                        text: 'Hủy',
                        btnClass: 'btn-dark',
                    }
                }
            });
        }
        else {
            saveOrderDetail();
        }
    }

    // kiểm tra site có cùng phương án có hiển thị popup confirm loại hàng hóa
    $.ajax({
        url: '/FullContainerDelivery/CheckCommodity?operMethod=' + mainScreen.slxOperMethod.option(opt.value),
        contentType: ajaxConfig.contentType,
        method: ajaxConfig.GET,
        success: function (response) {
            if (response && response.type.toLowerCase() === messageType.success) {
                checkApplyCommodity(response.isApplyCommodity);
            }
            else {
                message.error(response.message);
            }
        },
    });
}

// Sự kiện khi nhấn nút lưu
function onEdit(orderDetailNo) {
    isEdit = true;
    isContEdit = true;
    isFirstShowTransport = true;
    isClosePopup = false;
    isResetService = true;

    var oddId = mainScreen.txtOrderDetailId.option(devExtremeOptions.value);

    // Clear toàn bộ dữ liệu trước khi load thông tin mới
    resetControlAllPopup();

    if (orderDetailNo !== '') {
        httpClient.post('/FullContainerDelivery/GetOrderDetail', { 'orderDetailNo': orderDetailNo })
            .done((res) => {
                try {
                    if (res.Data) {
                        itemEdit = res.Data;
                        fillDataToForm(res.Data);
                        GetOrderDetailOgg();
                        GetOrderDetailProperties(oddId);
                        GetOrderDetailService();

                        if (isUserNewVerComdDecl === 'Y') {
                            onLoadData(res.Data.ORDER_ID, res.Data.ORDER_DETAIL_NO, res.Data.ITEM_NO);
                        }

                    } else {
                        message.error(res.ContentType);
                    }
                } catch (err) {
                    message.error(err);
                }
            })
            .fail((jqXhr, textStatus, errorThrow) => {
                message.error(textStatus + ': ' + errorThrow);
            });
    }
}

// Là nút sửa trên lưới - Lấy thông tin
function onEditByOddId(oddId) {
    isEdit = true;
    isContEdit = true;
    isFirstShowTransport = true;
    isClosePopup = false;
    isResetService = true;
    isValidCustom = false;
    let storageId = GenStorageId();
    mainScreen.txtStorageId.option(devExtremeOptions.value, storageId);

    LoadDataToCombobox(false);

    if (oddId !== 0) {

        httpClient.post('/FullContainerDelivery/GetOrderDetailByOddId', { 'oddId': oddId })
            .done((res) => {
                try {
                    if (res.Data) {
                        itemEdit = res.Data;
                        fillDataToForm(res.Data);
                        GetOrderDetailOgg();
                        GetOrderDetailProperties(oddId);
                        GetOrderDetailService();

                        if (isUserNewVerComdDecl === 'Y') {
                            onLoadData(res.Data.ORDER_ID, res.Data.ORDER_DETAIL_NO, res.Data.ITEM_NO);
                        }

                    } else {
                        message.error(res.ContentType);
                    }
                } catch (err) {
                    message.error(err);
                }
            })
            .fail((jqXhr, textStatus, errorThrow) => {
                message.error(textStatus + ': ' + errorThrow);
            });
    }
}

function fillDataToForm(data) {
    $('body').css('overflow', 'hidden');
    mainScreen.popupRegister.show();
    loadOrderDetailToForm(data);
}

function loadOrderDetailToForm(data) {
    let createdDate = new Date(parseInt(data.CREATED_DATE.substr(6)));

    mainScreen.txtRegisterID.option(opt.value, data.ORDER_DETAIL_ID);
    mainScreen.txtOrderDetailId.option(opt.value, data.ORDER_DETAIL_ID);
    mainScreen.txtOrderDetailNo.option(opt.value, data.ORDER_DETAIL_NO);
    mainScreen.dtxCreateDate.option(opt.value, createdDate.getFullYear() > 1900 ? createdDate : '');
    mainScreen.slxAgent.option(opt.value, data.AGENT + "|" + data.LINE_OPER);
    mainScreen.txtBookNo.option(opt.value, data.BOOKING_NO);
    mainScreen.slxVessel.option(opt.value, data.VES_ID);
    mainScreen.slxPortFrom.option(opt.value, data.LLPOD);
    mainScreen.slxPortTo.option(opt.value, data.FPOD);
    mainScreen.txtContainerNo.option(opt.value, data.ITEM_NO);
    mainScreen.txtSealNo.option(devExtremeOptions.value, data.SEAL_NO);
    mainScreen.slxIso.option(opt.value, data.ISO_CODE);
    mainScreen.numGrossWeight.option(opt.value, data.GROSS_WEIGHT);
    mainScreen.chkVGM.option(opt.value, data.WEIGHT_CERTIFIED === 1 ? true : false);
    mainScreen.txtCertifiedPlace.option(opt.value, data.CERTIFIED_PLACE);
    mainScreen.numMaxGross.option(opt.value, data.MAX_GROSS);
    mainScreen.slxTransportType.option(opt.value, data.TRANSPORT_TYPE);
    mainScreen.slxVoyageBarge.option(opt.value, data.VOYAGE_BARGE);
    mainScreen.slxDepot.option(opt.value, data.PLACE_OF_RECEIPT);
    if (ApplyImo == 'Y') {
        mainScreen.txtIMO.option(opt.value, data.HAZADOUS);
    } else {
        mainScreen.slxIMO.option(opt.value, data.HAZADOUS);
    }
    mainScreen.txtUNNO.option(opt.value, data.UNNO);
    mainScreen.slxMobilePhone.option(opt.value, data.PHONE_NUMBER);
    mainScreen.txtRemark.option(opt.value, data.REMARK);
    mainScreen.txtProd_Name.option(devExtremeOptions.value, data.HAZARDOUS_CONTENT);
    mainScreen.txtProd_Pack.option(devExtremeOptions.value, data.PACKAGE_TYPE);
    mainScreen.txtProd_OwnName.option(devExtremeOptions.value, data.EMERGENCY_PHONE);
    mainScreen.chkConfirm.option(devExtremeOptions.value, true);
    mainScreen.slxOperMethod.option(opt.value, data.OPER_METHOD);
    TempArray.humidity = data.HUMIDITY;
    TempArray.ventilation = data.VENT;
    TempArray.temp = data.TEMP;
    TempArray.unit = data.VENT_UNIT;
    TempArray.chkTemp = data.NO_PLUGIN_REQUIRED == 'Y' ? true : false;
    SetDataPopupTemperture(TempArray);

    SpecialService = data.SPECIAL_SERVICE;

    isoDefault = data.ISO_CODE;

    isValidCustom = trimEx(data.VALID_CUSTOMS_DECLARATION) === 'Y' || trimEx(data.VALID_CARGO_ID) === 'Y';

}



function loadData(orderId) {
    if (orderId && orderId > 0) {
        $.post('/FullContainerDelivery/LoadData', { 'orderId': orderId }, function (res) {
            try {
                if (res.type === messageType.error) {
                    message.error(res.content);
                } else {
                    mainScreen.lblInfoCustomer.text(res.model.MESSAGE);
                    mainScreen.txtMaSoThue.option('value', res.model.CUS_TAXFILENO);
                    mainScreen.nbxSoLuongContainer.option('value', res.model.ITEM_COUNT);
                    mainScreen.txtMalo.option(devExtremeOptions.value, res.model.ORDER_ID);
                    mainScreen.txtGhiChu.option(devExtremeOptions.value, res.model.NOTES);
                    const dataSource = {
                        store: {
                            type: 'array',
                            key: 'ORDER_DETAIL_ID',
                            data: res.model.OrderDetails
                        }
                    };
                    mainScreen.gridOrderDetails.option({
                        dataSource: dataSource
                    });
                    mainScreen.gridOrderDetails.clearFilter("row");
                    setDisableControl(res.model.OrderDetails);
                }
            } catch (err) {
                message.error(err);
            }
        });
    }
}

function onRefreshData() {
    showSpinner();
    location.reload();
}

// Import Excel
function onPopupImport() {
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);

    if (!orderId) {
        message.error("Vui lòng chọn lô hàng!");
        return;
    }
    var numOrder = mainScreen.nbxSoLuongContainer.option(opt.value);
    var orderDetails = mainScreen.gridOrderDetails.option("dataSource").store._array;
    if (orderDetails === undefined)
        orderDetails = mainScreen.gridOrderDetails.option("dataSource").store.data;
    if (numOrder === orderDetails.length) {
        message.warning("Bạn không thể đăng kí cont vượt quá số lượng đơn hàng");
        return;
    }

    mainScreen.popupImport.show();



    //const gridExcel = Factory.CreateDataGrid("#grid-import");

    if (useNewVerComd === "Y") {
        $('#grid-import').dxDataGrid("columnOption", "HazardousContentXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "HazardousContent", "visible", false);

        $('#grid-import').dxDataGrid("columnOption", "PackageTypeXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "PackageType", "visible", false);

        $('#grid-import').dxDataGrid("columnOption", "EmergencyPhoneXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "EmergencyPhone", "visible", false);
    }
    else {
        $('#grid-import').dxDataGrid("columnOption", "HazardousContentXls", "visible", true);
        $('#grid-import').dxDataGrid("columnOption", "HazardousContent", "visible", true);

        $('#grid-import').dxDataGrid("columnOption", "PackageTypeXls", "visible", true);
        $('#grid-import').dxDataGrid("columnOption", "PackageType", "visible", true);

        $('#grid-import').dxDataGrid("columnOption", "EmergencyPhoneXls", "visible", true);
        $('#grid-import').dxDataGrid("columnOption", "EmergencyPhone", "visible", true);


        $('#grid-import').dxDataGrid("columnOption", "CommodGroup", "visible", false);

        $('#grid-import').dxDataGrid("columnOption", "CommodCodeXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "CommodCode", "visible", false);

        $('#grid-import').dxDataGrid("columnOption", "CommodDescr", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "CommodEmergencyPhoneXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "CommodEmergencyPhone", "visible", false);

        $('#grid-import').dxDataGrid("columnOption", "CommodPackageTypeXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "CommodPackageType", "visible", false);

        $('#grid-import').dxDataGrid("columnOption", "CommodNoteXls", "visible", false);
        $('#grid-import').dxDataGrid("columnOption", "CommodNote", "visible", false);
    }


    $.ajax({
        url: '/FullContainerDelivery/GetTransportType',
        contentType: ajaxConfig.contentType,
        method: ajaxConfig.GET,
        success: function (response) {
            if (response && response.type.toLowerCase() === messageType.success) {
                transportTypes = response.data;
            }
        },
    })

    $.ajax({
        url: '/FullContainerDelivery/GetOperMethodConfig',
        contentType: ajaxConfig.contentType,
        method: ajaxConfig.POST,
        data: JSON.stringify({ 'transportType': 0 }),
        success: function (response) {
            if (response && response.type.toLowerCase() === messageType.success) {
                operMethods = response.data;
            }
        },
    })
}

function onPopupImportExcelHiding() {
    Factory.CreateFileUploader('#fileUpload').reset();
    Factory.CreateDataGrid("#grid-import").option({
        dataSource: null
    });
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    if (isSubmit === true) loadData(orderId);
}

function onShowPopupImportExcel() {
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    if (!orderId) {
        message.error("Vui lòng chọn lô hàng!");
        return;
    }
    Factory.CreateFileUploader('#fileUpload').reset();
    mainScreen.popupImport.show();

    var title = "Nhập dữ liệu từ tập tin Excel (Mã lô: " + mainScreen.txtMalo.option(opt.value);
    title += " - Mã số thuế: " + mainScreen.txtMaSoThue.option(opt.value);
    title += " - Số lượng: " + mainScreen.nbxSoLuongContainer.option(opt.value) + ")";

    mainScreen.popupImport.option('title', title);

    var confirmText = "(*) Tôi xác nhận ds container trên thuộc khu vực " + siteName + " như trên Booking hãng tàu cấp!";
    Factory.CreateCheckBox('#chkConfirmImport').option('text', confirmText);
}

function onHidePopupImportExcel() {
    Factory.CreateFileUploader('#fileUpload').reset();
    mainScreen.popupImport.hide();
}

function onBindingExcelData(e) {
    const fileUpload = Factory.CreateFileUploader('#fileUpload');
    const gridExcel = Factory.CreateDataGrid("#grid-import");
    const result = JSON.parse(e.request.response);
    if (!result) {
        message.error('Quá trình import không trả về kết quả.');
        fileUpload.reset();
        return;
    }
}
//-------
function onShowErrorMessage(element, row) {
    var msg = '<ul>';
    if (row.data && row.data.Messages && row.data.Messages.length > 0) {
        const arrMsg = _.values(row.data.Messages);
        $.each(arrMsg,
            function (index, item) {
                if (item && item.MessageType === 'error') {
                    msg += '<li style="color: #FF3333">' + item.MessageContent + '</li > '
                } else if (item && (item.MessageType === 'warning' || item.MessageType === 'confirm')) {
                    msg += '<li style="color: #FF8C00">' + item.MessageContent + '</li>'
                } else {
                    msg += '<li style="color: #008000">' + item.MessageContent + '</li>'
                }
            });
    }
    msg += '</ul>'
    element.html(msg);
}

function setOperMethodValue(rowData, value) {
    if (rowData.data !== undefined)
        row = rowData;
    if (value !== undefined) {
        row.data.TransportType = value;
        row.data.OperMethod = null;
    }
}

function onCellPreparedHighLight(obj) {
    if (obj) {
        if (obj.rowType === 'header')
            obj.cellElement.css('color', '#0000FF');
        else {
            if (obj.column.index > 0 && obj.column.dataField) {
                if (!obj.column.dataField.includes("Xls"))
                    obj.cellElement.css('background', '#FFFACD');
            }
        }
    }
}

// Popup đăng ký cont
function onPopupRegister(e) {
    let storageId = GenStorageId();
    mainScreen.txtStorageId.option(devExtremeOptions.value, storageId);
    createFlag = true;
    var numOrder = mainScreen.nbxSoLuongContainer.option(opt.value);
    if (mainScreen.gridOrderDetails.option("dataSource") != null) {
        var orderDetails = mainScreen.gridOrderDetails.option("dataSource").store._array;
        if (orderDetails === undefined)
            orderDetails = mainScreen.gridOrderDetails.option("dataSource").store.data;
        if (numOrder === orderDetails.length) {
            message.warning("Bạn không thể đăng kí cont vượt quá số lượng đơn hàng");
            return;
        }
    }

    isClosePopup = false;
    mainScreen.txtRegisterID.option(opt.value, 0);
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);

    isEdit = false;
    if (!orderId) {
        message.error("Vui lòng chọn lô hàng!");
        return;
    }

    $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/CheckValidateOrder',
        data: JSON.stringify({ 'orderId': orderId })
    }).done((res) => {
        try {
            if (res.type === messageType.error) {
                message.error(res.content);
                return;
            }
            else {
                onShowPopupRegister();
                showSpinner();
                setTimeout(function () {
                    LoadDataToCombobox(false);
                    setStateControlByUserRole();
                    onInitDefaultValue();
                    hideSpinner();
                }, 100)
            }
        } catch (err) {
            message.error(err);
        }
    });
}

function onInitDefaultValue() {
    mainScreen.txtRegisterID.option(opt.value, '');
    const visible = ['CTL'].includes(loggedSiteId.trim());
    if (visible) {
        mainScreen.txtDelivery.option(opt.value, '');
    }
    mainScreen.txtOrderDetailId.option(opt.value, '');
    mainScreen.txtOrderDetailNo.option(opt.value, '');
    mainScreen.dtxCreateDate.option(opt.value, new Date());
    mainScreen.slxOperMethod.option(opt.value, '');
    mainScreen.slxAgent.option(opt.value, '');
    mainScreen.txtBookNo.option(opt.value, '');
    mainScreen.slxVessel.option(opt.value, '');
    mainScreen.slxPortFrom.option(opt.value, '');
    mainScreen.slxPortTo.option(opt.value, '');
    mainScreen.txtContainerNo.option(opt.value, '');
    mainScreen.txtSealNo.option(devExtremeOptions.value, '');
    mainScreen.slxIso.option(opt.value, '');
    mainScreen.numGrossWeight.option(opt.value, 0);
    mainScreen.chkVGM.option(opt.value, false);
    mainScreen.numMaxGross.option(opt.value, 0);
    mainScreen.slxTransportType.option(opt.value, 0);
    mainScreen.slxVoyageBarge.option(opt.value, '');
    if (ApplyImo == 'Y') {
        mainScreen.txtIMO.option(opt.value, '');
    } else {
        mainScreen.slxIMO.option(opt.value, '');
    }
    mainScreen.txtUNNO.option(opt.value, '');
    mainScreen.slxMobilePhone.option(opt.value, '');
    mainScreen.txtRemark.option(opt.value, '');
    mainScreen.txtCertifiedPlace.option(opt.value, '');
    mainScreen.slxDepot.option(opt.value, receiveFrom);

    resetFormProductInfo();
    resetFormTemperature();
    GetRegisterCode();

    resetOverSize();
    resetSpecialStacking();

    isResetService = true;
}

function resetFormTemperature() {
    mainScreen.numTemp_Temperature.option(devExtremeOptions.value, '');
    mainScreen.txtTemp_Ventilation.option(devExtremeOptions.value, '');
    mainScreen.slxTemp_Unit.option(devExtremeOptions.value, '');
    mainScreen.numTemp_Humidity.option(devExtremeOptions.value, '');

    TempArray.humidity = '';
    TempArray.ventilation = '';
    TempArray.temp = '';
    TempArray.unit = '';
}

function resetFormProductInfo() {
    mainScreen.txtProd_Name.option(devExtremeOptions.value, '');
    mainScreen.txtProd_Pack.option(devExtremeOptions.value, '');
    mainScreen.txtProd_OwnName.option(devExtremeOptions.value, '');
}

function resetFormAfterRegistered() {
    mainScreen.txtCertifiedPlace.option(opt.value, '');
    mainScreen.slxIMO.option(opt.value, '');
    mainScreen.txtUNNO.option(opt.value, '');
    mainScreen.txtContainerNo.option(opt.value, '');
    mainScreen.chkVGM.option(opt.value, false);
    mainScreen.numMaxGross.option(opt.value, 0);
    resetFormProductInfo();
    resetFormTemperature();
    GetRegisterCode();
}

function setStateControlByUserRole() {
    if (userRole === 'ICD')
        mainScreen.slxDepot.option(opt.disabled, true);
    else
        mainScreen.slxDepot.option(opt.disabled, false);
}

function LoadDataToCombobox(async) {
    if (!asynchronize) {
        $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetAllDataInit'
        }).done((res) => {
            try {
                if (res.type === messageType.error) {
                    message.error(res.content);
                } else {
                    dsTransportTypes = res.transportTypes;
                    dsVesselList = res.vesselList;
                    dsLineopers = res.lineopers;
                    dsExtraPhones = res.extraPhones;
                    siteName = res.siteName;

                    mainScreen.slxTransportType.option(opt.dataSource, dsTransportTypes);
                    mainScreen.slxVoyageBarge.option(opt.dataSource, dsVesselList);
                    mainScreen.slxAgent.option(opt.dataSource, dsLineopers);

                    mainScreen.slxMobilePhone.option(opt.dataSource, dsExtraPhones);


                    dsIsoes = res.isoes;
                    dsImos = res.imos;
                    dsPortToByVesssels = res.portToByVesssels;
                    dsPlaceOfReceipts = res.placeOfReceipts;

                    mainScreen.slxIso.option(opt.dataSource, dsIsoes);
                    if (ApplyImo != 'Y') {
                        mainScreen.slxIMO.option(opt.dataSource, dsImos);
                    }
                    mainScreen.slxDepot.option(opt.dataSource, dsPlaceOfReceipts);

                    const dataSource = {
                        store: {
                            type: 'array',
                            key: 'ID',
                            data: dsPortToByVesssels
                        },
                        paginate: true,
                        pageSize: 50
                    };

                    mainScreen.slxPortTo.option(opt.dataSource, dataSource);
                    mainScreen.slxNewPortTo.option(opt.dataSource, dataSource);

                    dsVesselVoyages = res.vesselVoyages;
                    dsPhoneAntiCompany = res.phoneAntiCompanys;
                    dsServiceInspecteds = res.serviceInspecteds;

                    mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.dataSource, dsPhoneAntiCompany);
                    mainScreen.slxVessel.option(opt.dataSource, dsVesselVoyages);
                    mainScreen.rdxService_Inspected.option(opt.dataSource, dsServiceInspecteds);
                }
            } catch (err) {
                message.error(err);
            }
        });
    }

    if (asynchronize == false)
        asynchronize = true;
}

function GetRegisterCode() {
    $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/GetRegister',
        async: false
    }).done((res) => {
        try {
            if (res.type === messageType.error) {
                message.error("Không tạo được số đăng ký!");
            } else {
                mainScreen.txtOrderDetailNo.option(opt.value, res.registerCode);
            }
        } catch (err) {
            message.error(err);
        }
    });
}

function onClose() {
    let name = mainScreen.txtStorageId.option(devExtremeOptions.value);
    createFlag = false;
    localStorage.removeItem(name);
    isValidCustom = false;
    // Clear Control các popup
    resetControlAllPopup();
    //Xoá imo unno cũ
    listImoUnnoOrderDetail.length = 0;
    mainScreen.popupRegister.hide();
}

// Popup Thông tin hàng hóa
function onPopupProductInfor() {

    if (isUserNewVerComdDecl === 'Y') {
        onShowPopupCommodityInfo();
    }
    else {
        var imo = '';
        if (ApplyImo == 'Y') {
            imo = mainScreen.txtIMO.option(opt.value);
        } else {
            imo = mainScreen.slxIMO.option(opt.value);
        }
        let unno = mainScreen.txtUNNO.option(opt.value);
        let itemNo = mainScreen.txtContainerNo.option(opt.value);

        mainScreen.txtProd_ContainerNo.option(opt.value, itemNo);
        mainScreen.txtProd_IMO.option(opt.value, imo);
        mainScreen.txtProd_UNNO.option(opt.value, unno);

        mainScreen.popupProductInfor.show();
    }
}

function onCloseProductInfor() {
    mainScreen.popupProductInfor.hide();
    $('body').css('overflow', 'scroll');
}

// Popup Thêm IMO và UNNO
function onPopupImoUnno() {
    //nếu chỉnh sửa thì kiểm tra số đăng kí, còn thêm mới thì không cần
    if (createFlag == true) {
        resetImoPopup();
        loadListImoUnno();
        mainScreen.gridImoUnno.option(opt.dataSource, listImoUnnoOrderDetail);
        mainScreen.popupImoUnnoInfo.show();
    } else {
        var orderDetailNo = mainScreen.txtOrderDetailNo.option(opt.value);
        httpClient.post('/FullContainerDelivery/GetOrderDetail', { 'orderDetailNo': orderDetailNo })
            .done((res) => {
                try {
                    if (res.Data) {
                        if (res.Data.HAZADOUS_STATUS !== 'Y' && res.Data.PAYMENT_STATUS !== 'N') {
                            message.error("Thông báo lỗi: Không được thay đổi thông tin (SIZE,...) đối với phiếu đã thanh toán phí.");
                        } else {
                            resetImoPopup();
                            loadListImoUnno();
                            mainScreen.gridImoUnno.option(opt.dataSource, listImoUnnoOrderDetail);
                            mainScreen.popupImoUnnoInfo.show();
                        }
                    } else {
                        message.error(res.ContentType);
                    }
                } catch (err) {
                    message.error(err);
                }
            })
            .fail((jqXhr, textStatus, errorThrow) => {
                message.error(textStatus + ': ' + errorThrow);
            });
    }

}
//load thông tin imo unno của hệ thống
function loadListImoUnno() {
    $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/GetImos'
    }).done((res) => {
        try {
            if (res.type === messageType.error) {
                message.error(res.content);
            } else {
                dsImos = res.imos;
                mainScreen.slxImopopup.option(opt.dataSource, dsImos);
                unnoImoList = res.unnoImoList;
                if (isEdit == true) {
                    reLoadImoUnno(mainScreen.txtIMO.option(opt.value), mainScreen.txtUNNO.option(opt.value));
                }
            }
        } catch (err) {
            message.error(err);
        }
    });
}

function loadUnno() {
    let imo = mainScreen.slxImopopup.option(devExtremeOptions.value);
    if (imo != null || imo != "") {
        var list = unnoImoList.filter(function (el) {
            return el.HAZ_CLASS.trim() == imo;
        });
        mainScreen.slxUnno.option(opt.value, '');
        mainScreen.slxUnno.option(opt.dataSource, list);
        setAddImoButton();
    }
}
//load lại Imo và Unno lên lưới
function reLoadImoUnno(strImo, strUnno) {
    let codeImo = RemoveEndSemicolon(strImo).split(";");
    let codeUnno = RemoveEndSemicolon(strUnno).split(";");
    if (codeImo.length != codeUnno.length) {
        message.error("Lỗi Imo và Unno không khớp");
    } else {
        if (codeImo.length > 0 && codeImo[0] != "") {
            listImoUnnoOrderDetail = [];
            for (let i = 0; i < codeImo.length; i++) {
                let iMoUnno = unnoImoList.find(x => x.HAZ_CLASS.trim() == codeImo[i] && x.HAZ_UN_NO.trim() == codeUnno[i]);
                //Tránh trường hợp ai đó đã xoá một cặp imo unno dưới database
                if (typeof iMoUnno !== "undefined") {
                    listImoUnnoOrderDetail.push(iMoUnno);
                }
            }
        }
        mainScreen.gridImoUnno.option(opt.dataSource, listImoUnnoOrderDetail);
    }
}
function onCloseImoUnnoInfo() {
    var imos = '';
    var unnos = '';
    if (listImoUnnoOrderDetail.length >= 0) {
        listImoUnnoOrderDetail.forEach((element) => {
            imos += element.HAZ_CLASS.trim() + ';';
            unnos += element.HAZ_UN_NO.trim() + ';';
        });
        mainScreen.txtIMO.option(opt.value, RemoveEndSemicolon(imos));
        mainScreen.txtUNNO.option(opt.value, RemoveEndSemicolon(unnos));
    }
    mainScreen.popupImoUnnoInfo.hide();
}

function onRemoveListImos() {
    const selectedItems = mainScreen.gridImoUnno.getSelectedRowsData();
    var orderDetailNo = mainScreen.txtOrderDetailNo.option(opt.value);
    $.confirm({
        title: 'Xác nhận !',
        content: 'Xác nhận xóa ' + selectedItems.length + ' cặp IMO, UNNO khỏi danh sách',
        type: 'orange',
        typeAnimated: true,
        draggable: true,
        animationSpeed: 200,
        animation: 'zoom',
        closeAnimation: 'scale',
        buttons: {
            confirm: {
                text: 'Xác nhận',
                btnClass: 'btn-red',
                action: function () {
                    if (createFlag == true) {
                        RemoveListImo(selectedItems);
                    }
                    else if (selectedItems.length > 0) {
                        httpClient.post('/FullContainerDelivery/GetOrderDetail', { 'orderDetailNo': orderDetailNo })
                            .done((res) => {
                                try {
                                    if (res.Data) {
                                        if (res.Data.SO_PHIEU) {
                                            message.error("Cont đã vào Cảng không cho phép Chỉnh sửa IMO");
                                        } else if (res.Data.PAYMENT_STATUS != 'N' && (mainScreen.gridImoUnno.option("dataSource").length - selectedItems.length <= 0)) {
                                            message.error("Phiếu đã thanh toán phí IMO tối thiểu phải còn 1 cặp IMO, UNNO trong danh sách");
                                        } else {
                                            RemoveListImo(selectedItems);
                                        }
                                    } else {
                                        message.error(res.ContentType);
                                    }
                                } catch (err) {
                                    message.error(err);
                                }
                            })
                            .fail((jqXhr, textStatus, errorThrow) => {
                                message.error(textStatus + ': ' + errorThrow);
                            });
                    } else {
                        message.error("Không có cặp IMO UNNO nào được chọn");
                    }
                }
            },
            cancel: {
                text: 'Hủy',
                btnClass: 'btn-dark',
                action: function () {
                }
            }
        }
    });
}

function RemoveListImo(listSelectImo) {
    listSelectImo.forEach((element) => {
        listImoUnnoOrderDetail = listImoUnnoOrderDetail.filter((imo) => imo.ID !== element.ID);
    });
    mainScreen.gridImoUnno.option(opt.dataSource, listImoUnnoOrderDetail);
}
//xoá dấu ; cuối chuỗi string
function RemoveEndSemicolon(str) {
    if (str.substr(str.length - 1) == ';') {
        return str.slice(0, -1).trim();
    }
    return str.trim();
}

function onAddImo() {
    let imo = mainScreen.slxImopopup.option(devExtremeOptions.value);
    let unno = mainScreen.slxUnno.option(devExtremeOptions.value);
    var orderDetailNo = mainScreen.txtOrderDetailNo.option(opt.value);
    if (createFlag == true) {
        AddImoUnno(imo, unno);
    } else {
        httpClient.post('/FullContainerDelivery/GetOrderDetail', { 'orderDetailNo': orderDetailNo })
            .done((res) => {
                try {
                    if (res.Data) {
                        if (res.Data.SO_PHIEU) {
                            message.error("Cont đã vào Cảng không cho phép Chỉnh sửa IMO");
                        } else {
                            AddImoUnno(imo, unno);
                        }
                    } else {
                        message.error(res.ContentType);
                    }
                } catch (err) {
                    message.error(err);
                }
            })
            .fail((jqXhr, textStatus, errorThrow) => {
                message.error(textStatus + ': ' + errorThrow);
            });
    }
}

function AddImoUnno(imo, unno) {
    if ((imo != '' && unno != '') && (imo != null && unno != null)) {
        var iMoUnno = unnoImoList.find(x => x.HAZ_CLASS.trim() == imo && x.HAZ_UN_NO.trim() == unno);
        var iMoUnooEx = listImoUnnoOrderDetail.find(x => x.HAZ_CLASS.trim() == imo && x.HAZ_UN_NO.trim() == unno);
        if (iMoUnooEx != undefined) {
            message.error('Thông tin cặp IMO, UNNO này đã tồn tại');
        } else {
            listImoUnnoOrderDetail.unshift(iMoUnno);
            mainScreen.gridImoUnno.option(opt.dataSource, listImoUnnoOrderDetail);
            setDeleteImoButton();
            setAddImoButton();
        }
    }
}
function setDeleteImoButton() {
    let selectedItems = mainScreen.gridImoUnno.getSelectedRowsData();
    if (selectedItems.length <= 0) {
        mainScreen.btnXoaImo.option('disabled', true);
    } else {
        mainScreen.btnXoaImo.option('disabled', false);
    }
}

function setAddImoButton() {
    let imo = mainScreen.slxImopopup.option(devExtremeOptions.value);
    let unno = mainScreen.slxUnno.option(devExtremeOptions.value);
    if ((imo != '' && unno != '') && (imo != null && unno != null) && listImoUnnoOrderDetail.length < MaxImo) {
        mainScreen.btnThemImo.option('disabled', false);
    } else {
        mainScreen.btnThemImo.option('disabled', true);
    }
}

function resetImoPopup() {
    setDeleteImoButton();
    mainScreen.slxImopopup.option(opt.value, '');
    mainScreen.slxUnno.option(opt.value, '');
    setAddImoButton();
}

function onUpdateProductInfor() {
    mainScreen.popupProductInfor.hide();
}

// Popup Danh sách dịch vụ
function onPopupRegisterService() {
    GetOrderDetailService();

    //chặn chọn khử trùng các site khác CTL, GNL
    const visible = ['CTL', 'GNL'].includes(loggedSiteId.trim());
    if (!visible) {
        document.getElementById('divAntiseptic').style.display = 'none';
    }
    mainScreen.popupRegisterService.show();
}

function onCloseRegisterService() {
    isResetService = true;
    mainScreen.popupRegisterService.hide();
    //onResetAntiseptic();
    $('body').css('overflow', 'scroll');
}

function onUpdateRegisterService() {
    isResetService = false;
    let storageId = mainScreen.txtStorageId.option(devExtremeOptions.value);

    let services = [];
    var service = mainScreen.rdxService_Inspected.option(devExtremeOptions.value);
    var isKhuTrung = mainScreen.chkSevice_Antiseptic.option(devExtremeOptions.value);
    var phonNumber = mainScreen.slxService_PhoneNumberAntiDepartment.option(devExtremeOptions.value);
    if (service == false)
        service = '';
    services.push(service);
    services.push(phonNumber);
    services.push(isKhuTrung);

    localStorage.setItem(storageId, JSON.stringify(services));

    mainScreen.popupRegisterService.hide();
}

// Popup Xếp đỡ đặc biệt

function resetSpecialStacking() {
    $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/ListSpecialStacking',
        async: false
    }).done((res) => {
        try {
            if (res.type === messageType.error) {
                message.error(res.content);
            } else {
                leftItems = res.model;
                rightItems = new Array();
            }
        } catch (err) {
            message.error(err);
        }
    });
}

function onPopupSpecialStacking() {
    mainScreen.popupSpecialStacking.show();
    ReloadPopupSpecialStacking();
    ResetPopupSpecialStacking();
}

function ResetPopupSpecialStacking() {
    mainScreen.listService.option('items', leftItems);
    mainScreen.selectedService.option('items', rightItems);
}

function onCloseSpecialStacking() {
    $('body').css('overflow', 'scroll');

    ResetPopupSpecialStacking();

    ReloadPopupSpecialStacking();

    mainScreen.popupSpecialStacking.hide();
}

function GetOrderDetailProperties(oddId) {
    if (oddId != "") {
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetOrderDetailPropertiesByOrderDetailId',
            data: JSON.stringify({ 'OddId': oddId })
        }).done((respJson) => {
            if (respJson) {
                if (respJson.type === messageType.success) {
                    var data = respJson.content;
                    if (data != null && data.length > 0) {

                        // xữ lý DS bên trái
                        data.forEach(function (i) {
                            mainScreen.listService.deleteItem(i);
                        });

                        leftItems = mainScreen.listService.option('items');

                        // xữ lý DS bên phải
                        rightItems = data;

                        mainScreen.selectedService.option({
                            dataSource: rightItems
                        });
                    } else {
                        //Khi cont không có đăng ký xếp dỡ đặc biệt sẽ thực hiện lấy thông tin
                        resetSpecialStacking()
                    }
                } else {
                    message.error(respJson.content);
                }
            }
        });
    }
}

function OnSaveSelected() {
    leftItems = mainScreen.listService.option('items');
    rightItems = mainScreen.selectedService.option('items');

    //Reload
    ReloadPopupSpecialStacking();

    mainScreen.popupSpecialStacking.hide();
}

function MoveToRight() {
    var right = 'selectedService';
    var left = 'listService';

    MoveItems(left, right, false);
    UpdateButtonState();
}

function MoveToLeft() {
    var right = 'selectedService';
    var left = 'listService';

    MoveItems(right, left, false);
    UpdateButtonState();
}

function MoveAllToRight() {
    var right = 'selectedService';
    var left = 'listService';

    MoveItems(left, right, true);
    UpdateButtonState();
}

function MoveAllToLeft() {
    var right = 'selectedService';
    var left = 'listService';

    MoveItems(right, left, true);
    UpdateButtonState();
}

function MoveItems(beginName, endName, isMoveAll) {
    var begin = Factory.CreateList('#' + beginName);
    var end = Factory.CreateList('#' + endName);

    if (isMoveAll) {
        var selecteds = begin.option('items');
    } else {
        var selecteds = begin.option('selectedItems');
    }

    var beginPoint = begin.option('items');
    var endPoint = end.option('items');;

    var dataBegin = new Array();
    var dataEnd = new Array();

    if (selecteds && selecteds.length > 0) {
        //Lấy DS lúc đầu của nơi chuyển đến.
        for (var i = 0; i < endPoint.length; i++) {
            dataEnd.push(endPoint[i]);
        }

        // Lấy Toàn bộ DS bên bắt đầu
        for (var i = 0; i < beginPoint.length; i++) {
            dataBegin.push(beginPoint[i]);
        }
        //add value into right
        for (var i = 0; i < selecteds.length; i++) {
            dataEnd.push(selecteds[i]);
        }
        end.option('items', dataEnd);

        //remove value in left
        for (var i = selecteds.length; i > 0; i--) {
            var index = dataBegin.indexOf(selecteds[i - 1]);
            if (index > -1) {
                dataBegin.splice(index, 1);
            }
        }
        begin.option('items', dataBegin);
    }

    ReloadPopupSpecialStacking();
}

function ReloadPopupSpecialStacking() {
    mainScreen.listService.option('selectedItems', '')
    mainScreen.selectedService.option('selectedItems', '')
}

function UpdateButtonState() {
    mainScreen.btnMoveAllItemsToRight.option(devExtremeOptions.disabled, mainScreen.listService.option('items') <= 0);
    mainScreen.btnMoveAllItemsToLeft.option(devExtremeOptions.disabled, mainScreen.selectedService.option('items') <= 0);
    mainScreen.btnMoveSelectedItemsToRight.option(devExtremeOptions.disabled, mainScreen.listService.option('selectedItems') <= 0);
    mainScreen.btnMoveSelectedItemsToLeft.option(devExtremeOptions.disabled, mainScreen.selectedService.option('selectedItems') <= 0);
}

//-----------------------------------
//Delete 1 phiếu trên lưới
function onDelete(orderDetailId) {
    if (orderDetailId) {
        $.confirm({
            title: 'Xác nhận !',
            content: 'Bạn muốn xóa phiếu đã chọn ?',
            type: 'orange',
            typeAnimated: true,
            draggable: true,
            animationSpeed: 200,
            animation: 'zoom',
            closeAnimation: 'scale',
            buttons: {
                confirm: {
                    text: 'Xác nhận',
                    btnClass: 'btn-red',
                    action: function () {
                        httpClient.post('/FullContainerDelivery/OnDelete', { 'orderDetailId': orderDetailId })
                            .done((res) => {
                                try {
                                    if (res.type === messageType.success) {
                                        const orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
                                        message.success("Xóa phiếu thành công");

                                        loadData(orderId);
                                    } else {
                                        message.error(res.content);
                                    }
                                } catch (err) {
                                    message.error(err);
                                }
                            })
                            .fail((jqXhr, textStatus, errorThrow) => {
                                message.error(textStatus + ': ' + errorThrow);
                            });
                    }
                },
                cancel: {
                    text: 'Hủy',
                    btnClass: 'btn-dark',
                    action: function () {
                    }
                }
            }
        });
    } else {
        message.error("Không tìm được số đăng ký.");
    }
}

function onRemoveListRegister() {
    const selectedItems = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (selectedItems.length > 0) {
        var orderDetailIds = new Array();
        for (var i = 0; i < selectedItems.length; i++) {
            orderDetailIds.push(selectedItems[i].ORDER_DETAIL_ID);
        }
        $.confirm({
            title: 'Xác nhận !',
            content: 'Bạn muốn xóa container đã chọn ?',
            type: 'orange',
            typeAnimated: true,
            draggable: true,
            animationSpeed: 200,
            animation: 'zoom',
            closeAnimation: 'scale',
            buttons: {
                confirm: {
                    text: 'Xác nhận',
                    btnClass: 'btn-red',
                    action: function () {
                        httpClient.post('/FullContainerDelivery/OnMultiDelete', { 'orderDetailIds': orderDetailIds })
                            .done((res) => {
                                try {
                                    if (res.type === messageType.success) {
                                        const orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
                                        message.success("Xóa phiếu thành công");
                                        loadData(orderId);
                                    } else {
                                        message.error(res.content);
                                    }
                                } catch (err) {
                                    message.error(err);
                                }
                            })
                            .fail((jqXhr, textStatus, errorThrow) => {
                                message.error(textStatus + ': ' + errorThrow);
                            });
                    }
                },
                cancel: {
                    text: 'Hủy',
                    btnClass: 'btn-dark',
                    action: function () {
                    }
                }
            }
        });
    } else {
        message.warning("Vui lòng chọn container cần xóa");
    }
}

function onPrintOrder(odId) {
    httpClient.post('/FullContainerDelivery/BeforePrintListRegister', { 'odIds': odId })
        .done((res) => {
            try {
                if (res) {
                    if (res.type === messageType.success) {
                        mainScreen.popupReport.show();
                    } else {
                        message.error(res.message);
                    }
                }
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        });
}

function onPrintListRegister() {
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);

    if (!orderId) {
        message.error("Vui lòng chọn lô hàng!");
        return;
    }

    var orderdetailList = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (orderdetailList.length <= 0) {
        message.error("Vui lòng chọn ít nhất một container để in phiếu.");
    } else {
        var odIds = '';
        for (var i = 0; i < orderdetailList.length - 1; i++) {
            odIds += orderdetailList[i].ORDER_DETAIL_ID + ',';
        }
        odIds += orderdetailList[orderdetailList.length - 1].ORDER_DETAIL_ID;

        httpClient.post('/FullContainerDelivery/BeforePrintListRegister', { 'odIds': odIds })
            .done((res) => {
                try {
                    if (res.type === messageType.success) {
                        mainScreen.popupReport.show();
                    } else {
                        message.error(ress.content);
                    }
                } catch (err) {
                    message.error(err);
                }
            })
            .fail((jqXhr, textStatus, errorThrow) => {
                message.error(textStatus + ': ' + errorThrow);
            });
    }
}

function onPopupPrintingHiding() {
    loadData(mainScreen.txtMalo.option('value'));
}

function onPayment() {
    payment(false)
}

function payment(isContinuePayment) {
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    if (orderId === null || orderId === '') {
        message.error('Vui lòng chọn lô hàng.');
        return;
    }

    const selectedItems = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (!selectedItems) {
        message.error("Vui lòng chọn các phiếu cần thực hiện.");
        return;
    }

    var oddIds = new Array();
    for (var i = 0; i < selectedItems.length; i++) {
        oddIds.push(selectedItems[i].ORDER_DETAIL_ID);
    }

    $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/Payment',
        data: JSON.stringify({ 'orderId': orderId, 'oddIds': oddIds, 'isContinuePayment': isContinuePayment })
    }).done((respJson) => {
        if (respJson) {
            continePayment = {};
            continePayment.reponse = respJson;
            continePayment.execute = function (respJson) {
                open('/Checkout?oid=' + respJson.orderId + '&pid=' + respJson.pid, "_self");
            }

            if (respJson.type === messageType.success) {
                continePayment.execute(continePayment.reponse);
            }
            else if (respJson.type === messageType.error && respJson.errorCode === 'UNDECLARED_COMMODITY') {

                let dataSource = {
                    store: {
                        type: 'array',
                        key: 'ItemNo',
                        data: respJson.data
                    }
                };

                mainScreen.listErrorGrid.option({
                    dataSource: dataSource
                });

                mainScreen.popupListErrorPopup.show();
                return;
            }
            else if (respJson.type === messageType.confirm && respJson.errorCode === 'UNCHARGE_CONTAINER') {
                mandatoryDeclarationAndInfrasFee(respJson);
            }
            else if (respJson.type === messageType.confirm && respJson.errorCode === 'CONTINUES_PAYMENT' && !isContinuePayment) {
                $.confirm({
                    title: 'Thông báo',
                    content: respJson.message,
                    type: 'orange',
                    typeAnimated: true,
                    draggable: true,
                    animationSpeed: 200,
                    animation: 'zoom',
                    closeAnimation: 'scale',
                    buttons: {
                        confirm: {
                            text: 'Tiếp tục tính phí',
                            btnClass: 'btn-blue',
                            action: function () {
                                payment(true);
                            }
                        },
                        cancel: {
                            text: 'Hủy',
                            btnClass: 'btn-default',
                        }
                    }
                });
            }
            else if (mandatoryDeclarationAndInfrasFee(respJson)) {
                message.error(respJson.content);
            }
        }
    }).fail((jqXhr, textStatus, errorThrow) => {
        message.error(textStatus + ': ' + errorThrow);
    });
}

function onPrintBill() {
    const selectedItems = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (!selectedItems) {
        message.error("Vui lòng chọn các phiếu cần thực hiện");
        return;
    }
    return $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/GetPrintBill',
        data: JSON.stringify({ 'data': selectedItems })
    }).done((respJson) => {
        if (respJson) {
            if (respJson.type === messageType.success) {
                window.open("/Checkout/Receipt?pid=" + respJson.data);
            } else {
                message.warning(respJson.content);
            }
        }
    }).fail((jqXhr, textStatus, errorThrow) => {
        message.error(textStatus + ':' + errorThrow);
    });
}

function onStopCont() {
    const selectedItems = mainScreen.gridOrderDetails.getSelectedRowsData();
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    var orderdetailList = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (orderdetailList && orderdetailList.length > 0) {
        var strOrderDetailNo = '';
        for (var i = 0; i < orderdetailList.length - 1; i++) {
            strOrderDetailNo += orderdetailList[i].ORDER_DETAIL_NO + ',';
        }
        strOrderDetailNo += orderdetailList[orderdetailList.length - 1].ORDER_DETAIL_NO;
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetStopContainer',
            data: JSON.stringify({ 'orderDetails': selectedItems, 'orderId': orderId })
        }).done((respJson) => {
            if (respJson) {
                if (respJson.type === messageType.success) {
                    message.success(respJson.content);

                    loadData(orderId);
                } else {
                    message.error(respJson.content);
                }
            }
        });
    } else {
        message.warning('Vui lòng chọn ít nhất một container để Stop cont.');
        return;
    }
}

function onCancelStopCont() {
    var orderdetailList = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (orderdetailList && orderdetailList.length > 0) {
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetCancelStopContainer',
            data: JSON.stringify({ 'orderDetails': orderdetailList })
        }).done((respJson) => {
            if (respJson) {
                if (respJson.type === messageType.success) {
                    mainScreen.popupStopCont.show();
                } else {
                    message.error(respJson.content);
                }
            }
        });
    } else {
        message.warning('Vui lòng chọn ít nhất một container để Hủy stop.');
        return;
    }
}

function onPopupStopContHiding() {
    $('#lbllblNotify').text('').css('display', 'none');
    Factory.CreateButton('#btn-send-OTP-stop-cont').option(devExtremeOptions.disabled, false);
    Factory.CreateTextBox('#code-stop-cont').option(devExtremeOptions.value, '');

    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    loadData(orderId);
}

function onSendOTPStopCont() {
    var orderdetailList = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (orderdetailList && orderdetailList.length > 0) {
        var strOrderDetailID = '';
        for (var i = 0; i < orderdetailList.length - 1; i++) {
            strOrderDetailID += orderdetailList[i].ORDER_DETAIL_ID + ',';
        }
        strOrderDetailID += orderdetailList[orderdetailList.length - 1].ORDER_DETAIL_ID;
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/Registration/SendOTP',
            data: JSON.stringify({ 'orderDetailIds': strOrderDetailID })
        }).done((respJson) => {
            if (respJson) {
                if (respJson.MessageType === messageType.success) {
                    Factory.CreateButton('#btn-send-OTP-stop-cont').option(devExtremeOptions.disabled, true);

                    $('#lblNotify').html(respJson.MessageContent).css('display', 'block');
                    Factory.CreateTextBox('#code-stop-cont').focus();
                    setTimeout(function () {
                        Factory.CreateButton('#btn-send-OTP-stop-cont').option(devExtremeOptions.disabled, false);
                    },
                        50000);
                } else {
                    $('#lbllblNotify').text(respJson.MessageContent).css('display', 'block');
                }
            }
        });
    }
}

function onApprovalCodeStopCont() {
    var value = Factory.CreateTextBox('#code-stop-cont').option(opt.value);
    var txtCodeStopCont = value === null ? '' : value;
    var orderdetailList = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (orderdetailList && orderdetailList.length > 0) {
        var strOrderDetailID = '';
        for (var i = 0; i < orderdetailList.length - 1; i++) {
            strOrderDetailID += orderdetailList[i].ORDER_DETAIL_ID + ',';
        }
        strOrderDetailID += orderdetailList[orderdetailList.length - 1].ORDER_DETAIL_ID;
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/Registration/ValidateOtpCode',
            data: JSON.stringify({ 'orderDetailIds': strOrderDetailID, 'otpCode': trimEx(txtCodeStopCont).toUpperCase() })
        }).done((respJson) => {
            if (respJson) {
                if (respJson.MessageType === messageType.success) {
                    message.success(respJson.MessageContent);
                } else {
                    message.error(respJson.MessageContent);
                }
            }
        });
    } else {
        message.warning('Vui lòng chọn ít nhất một container để Hủy stop.');
        return;
    }
}

function onClosePopupStopCont() {
    mainScreen.popupStopCont.hide();
}

function onCargoCustoms() {
    var malo = mainScreen.txtMalo.option(devExtremeOptions.value);
    if (malo === null || malo === '') {
        message.error('Vui lòng chọn lô hàng.');
        return;
    }
    showSpinner();
    window.open(window.location.origin + "/Customs39/Browser?orderId=" + malo + "&operType=FRECV", "_self");
}

function onExportExcel() {
    mainScreen.gridOrderDetails.exportToExcel(false);
}

function onVgmCheckChange() {
    var isVgm = mainScreen.chkVGM.option(devExtremeOptions.value);
    mainScreen.txtCertifiedPlace.option(devExtremeOptions.disabled, !isVgm);
    mainScreen.numMaxGross.option(devExtremeOptions.disabled, !isVgm);
}

// Check value null
function checkConfirmStatus() {
    var isConfirm = mainScreen.chkConfirm.option(devExtremeOptions.value);
    //Kiểm tra check xác nhận
    if (!isConfirm) {
        return 'Vui lòng chọn xác nhận khu vực trước khi thực hiện đăng ký!';
    }
    return "";
}
// ********************* Load thông tin *************************

// load Phương án theo phương tiện
function onTransportTypeChange() {
    var transportType = mainScreen.slxTransportType.option(devExtremeOptions.value);

    if (transportType === 2) {
        mainScreen.slxVoyageBarge.option('disabled', false);
        // phương tiện là sà lan thì chặn ko cho chọn khử trùng
        mainScreen.chkSevice_Antiseptic.option("disabled", true);
        mainScreen.chkSevice_Antiseptic.option("value", false);
        mainScreen.slxService_PhoneNumberAntiDepartment.option("value", null);
    } else {
        mainScreen.slxVoyageBarge.option(devExtremeOptions.value, null);
        mainScreen.slxVoyageBarge.option('disabled', true);
        mainScreen.chkSevice_Antiseptic.option("disabled", false);
    }

    if (transportType === 2 && userRole === 'CUST')
        mainScreen.slxDepot.option(devExtremeOptions.disabled, false);
    else
        mainScreen.slxDepot.option(devExtremeOptions.disabled, true);

    if (transportType != null) {
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetOperMethodConfig',
            data: JSON.stringify({ 'transportType': transportType }),
            async: false
        }).done((res) => {
            if (res) {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'Key',
                        data: res.data
                    }
                };
                mainScreen.slxOperMethod.option({
                    dataSource: dataSource
                });
                if (res.data.length === 1) {
                    mainScreen.slxOperMethod.option(opt.value, res.data[0].Key);
                }
            }
        });
    }
}

// Chảng chuyển tải change
function onPortChange() {
    var transportType = mainScreen.slxTransportType.option(devExtremeOptions.value);
    var vessId = mainScreen.slxVessel.option(devExtremeOptions.value);
    var portCode = mainScreen.slxPortFrom.option(devExtremeOptions.value);
    var operMethod = mainScreen.slxOperMethod.option(devExtremeOptions.value);

    if (transportType != null) {
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetOperMethodConfig',
            data: JSON.stringify({ 'transportType': transportType, 'vessId': vessId, 'portCode': portCode, 'operMethod': operMethod }),
            async: false
        }).done((res) => {
            if (res) {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'Key',
                        data: res.data
                    }
                };
                mainScreen.slxOperMethod.option({
                    dataSource: dataSource
                });
                if (res.data.length === 1) {
                    mainScreen.slxOperMethod.option(opt.value, res.data[0].Key);
                }
            }
        });
    }
}

// load Phương tiện
function LoadTransportType() {
    return httpClient.get('/FullContainerDelivery/GetTransportType')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'ID',
                        data: res.data
                    }
                };
                mainScreen.slxTransportType.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

// load Hãng tàu / Đại lý
function LoadAgent() {
    return httpClient.post('/FullContainerDelivery/GetAgent')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'ID',
                        data: res.data
                    }
                };
                mainScreen.slxAgent.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

// load Tàu / chuyến
function LoadVesselVoyage() {
    return httpClient.post('/FullContainerDelivery/GetVesselVoyage')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'VOYAGE_ID',
                        data: res.data
                    }
                };
                mainScreen.slxVessel.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

// load Cảng chuyền tải  , Cảng đích-> theo tàu chuyến
function onVesselVotageChange() {
    var vesId = mainScreen.slxVessel.option(devExtremeOptions.value);

    // kiểm tra tàu chuyến có bị lock hay ko
    if (checkVesseLock(vesId))
        return;

    // cảng chuyền tải
    httpClient.get('/FullContainerDelivery/GetPortFromByVesId?vesId=' + vesId).done(
        (res) => {
            if (res) {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'PORT_CODE',
                        data: res.data
                    }
                };
                mainScreen.slxPortFrom.option({
                    dataSource: dataSource
                });
                const visible = ['CTL'].includes(loggedSiteId.trim());
                if (visible) {
                    if (res.data.length > 0 && res.data[0].SITE_ID) {
                        mainScreen.txtDelivery.option('value', res.data[0].SITE_ID);
                    }
                }
            }
            
        });
    // cảng đích
    httpClient.post('/FullContainerDelivery/GetPortTo', { 'vesId': vesId, }).done(
        (res) => {
            if (res) {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'PORT_CODE',
                        data: res.data
                    }
                };
                mainScreen.slxPortTo.option({
                    dataSource: dataSource
                });
            }
        });
}

// load Kích cỡ
function LoadIso() {
    return httpClient.post('/FullContainerDelivery/GetIso')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'ISO',
                        data: res.data
                    }
                };
                mainScreen.slxIso.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

// load Chuyến
function LoadVoyageBarge() {
    return httpClient.post('/FullContainerDelivery/GetVoyageBarge')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'Key',
                        data: res.data
                    }
                };
                mainScreen.slxVoyageBarge.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

// load Nhận từ
function LoadDepot() {
    return httpClient.post('/FullContainerDelivery/GetDepot')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'ID',
                        data: res.data
                    }
                };
                mainScreen.slxDepot.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

// load IMO
function LoadImo() {
    return httpClient.post('/FullContainerDelivery/GetIMO')
        .done((res) => {
            try {
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'HAZ_CLASS',
                        data: res.data
                    }
                };
                mainScreen.slxIMO.option({
                    dataSource: dataSource
                });
            } catch (err) {
                message.error(err);
            }
        })
        .fail((jqXhr, textStatus, errorThrow) => {
            message.error(textStatus + ': ' + errorThrow);
        })
}

function onSendSMS() {
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    var orderdetailList = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (orderdetailList && orderdetailList.length > 0) {
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/SendSms',
            data: JSON.stringify({ 'orderId': orderId, 'orderDetails': orderdetailList })
        }).done((respJson) => {
            if (respJson) {
                if (respJson.type === messageType.success) {
                    message.success(respJson.content);
                } else {
                    message.error(respJson.content);
                }
            }
        });
    } else {
        message.warning('Vui lòng chọn ít nhất một phiếu đăng ký để gửi SMS.');
        return;
    }
}

function convertTextDateToDate(value) {
    if (value === null || value === undefined || value === '')
        return '';
    var fullDate = new Date(value.match(/-?\d+/)[0] * 1);
    if (fullDate.getFullYear() <= 1900)
        return '';
    return fullDate;
}

function isNull(value) {
    if (value === null)
        return '';
    return value;
}

function trimEx(value) {
    if (!value) return '';
    return value.trim();
}

function onShowPopupRegister() {
    mainScreen.popupRegister.show();
    $("#dxScrollViewMultiEdit").dxScrollView();
    $('body').css('overflow', 'hidden');
}


function onActiveScrollViewMultiEdit() {
    $("#dxScrollViewMultiEdit").dxScrollView();
}

function SetDataPopupTemperture(TempArray) {
    mainScreen.numTemp_Temperature.option(opt.value, TempArray.temp);
    mainScreen.txtTemp_Ventilation.option(opt.value, TempArray.ventilation);
    mainScreen.slxTemp_Unit.option(opt.value, TempArray.unit);
    mainScreen.numTemp_Humidity.option(opt.value, TempArray.humidity);
    mainScreen.chkTemp_Temperature.option(opt.value, TempArray.chkTemp);
}

function onPopupTemperatureInfor() {
    let containerNo = mainScreen.txtContainerNo.option(devExtremeOptions.value);
    mainScreen.txtTemp_ContainerNo.option(opt.value, containerNo);
    SetDataPopupTemperture(TempArray);
    mainScreen.popupTemperatureInfor.show();
}

function onClosePopupTemperatureInfor() {
    //reset popup
    SetDataPopupTemperture(TempArray);
    mainScreen.popupTemperatureInfor.hide();
    $('body').css('overflow', 'scroll');
}

function onSavePopupTemperatureInfor() {
    TempArray.temp = mainScreen.numTemp_Temperature.option(opt.value);
    TempArray.ventilation = mainScreen.txtTemp_Ventilation.option(opt.value);
    TempArray.unit = mainScreen.slxTemp_Unit.option(opt.value);
    TempArray.humidity = mainScreen.numTemp_Humidity.option(opt.value);
    TempArray.chkTemp = mainScreen.chkTemp_Temperature.option(opt.value);

    SetDataPopupTemperture(TempArray);
    mainScreen.popupTemperatureInfor.hide();
}

function onChangePopup_ChkTemperture() {
    var isSetTemp = mainScreen.chkTemp_Temperature.option(devExtremeOptions.value);

    if (isSetTemp) {
        mainScreen.numTemp_Temperature.option(devExtremeOptions.disabled, true);
        mainScreen.numTemp_Temperature.option(devExtremeOptions.value, null);
    } else {
        mainScreen.numTemp_Temperature.option(devExtremeOptions.disabled, false);
    }
}

function SetDataPopupOversize(osArray) {
    var data = Object.assign({}, osArray);
    if (data.top != 0 || data.right != 0 || data.left != 0 || data.behind != 0 || data.front != 0) {
        $('#txtOversize_Opt1').prop("checked", true);
        OnChangeOversize(1);
    }
    else {
        $('#txtOversize_Opt2').prop("checked", true);
    }
        

    mainScreen.numOversize_Top.option(opt.value, data.top == null ? 0 : data.top);
    mainScreen.numOversize_Right.option(opt.value, data.right == null ? 0 : data.right);
    mainScreen.numOversize_Left.option(opt.value, data.left == null ? 0 : data.left);
    mainScreen.numOversize_Behind.option(opt.value, data.behind == null ? 0 : data.behind);
    mainScreen.numOversize_Front.option(opt.value, data.front == null ? 0 : data.front);
    mainScreen.numOversize_Length.option(opt.value, data.length == null ? 0 : data.length);
    mainScreen.numOversize_Height.option(opt.value, data.height == null ? 0 : data.height);
    mainScreen.numOversize_Width.option(opt.value, data.width == null ? 0 : data.width);
    mainScreen.chkOversize_NoOversize.option(opt.value, data.noOverSize);
}

function onPopupOversizeInfor() {
    GetOrderDetailOgg();
    let containerNo = mainScreen.txtContainerNo.option(devExtremeOptions.value);
    let iso = mainScreen.slxIso.option(devExtremeOptions.value);
    
    mainScreen.txtOversize_ContainerNo.option(opt.value, containerNo);
    mainScreen.txtOversize_ISO.option(opt.value, iso);
    mainScreen.txtOversize_Unit.option(opt.value, 'C');

    mainScreen.popupOversizeInfor.show();
}

function onUpdateOversizeInfor() {
    var top = mainScreen.numOversize_Top.option(opt.value);
    var right = mainScreen.numOversize_Right.option(opt.value);
    var left = mainScreen.numOversize_Left.option(opt.value);
    var behind = mainScreen.numOversize_Behind.option(opt.value);
    var front = mainScreen.numOversize_Front.option(opt.value);
    var lenght = mainScreen.numOversize_Length.option(opt.value);
    var height = mainScreen.numOversize_Height.option(opt.value);
    var width = mainScreen.numOversize_Width.option(opt.value);
    var noOverSize = mainScreen.chkOversize_NoOversize.option(opt.value);

    if (!noOverSize) {
        var chkOogOH = mainScreen.chkOversize_OH.option(opt.value);
        var chkOogOL = mainScreen.chkOversize_OL.option(opt.value);
        var chkOogOW = mainScreen.chkOversize_OW.option(opt.value);

        if (!chkOogOW && !chkOogOH && !chkOogOL) {
            $('#lblErrorOverSize').text("Nếu không phải là cont quá khổ vui lòng check \"Không quá khổ\"");
            return;
        }
    }

    // Làm tròn tất cả các giá trị đến 1 số lẻ thập phân
    function roundToOneDecimal(value) {
        return value ? Number(value.toFixed(1)) : 0;  // Nếu giá trị null hoặc undefined, mặc định là 0
    }

    overSizeArrayOfPopup.top = top;
    overSizeArrayOfPopup.right = right;
    overSizeArrayOfPopup.left = left;
    overSizeArrayOfPopup.behind = behind;
    overSizeArrayOfPopup.front = front;
    overSizeArrayOfPopup.length = roundToOneDecimal(lenght);
    overSizeArrayOfPopup.height = roundToOneDecimal(height);
    overSizeArrayOfPopup.width = roundToOneDecimal(width);
    overSizeArrayOfPopup.noOverSize = noOverSize;

    isoDefault = mainScreen.slxIso.option(devExtremeOptions.value);

    $('#lblErrorOverSize').text("");

    mainScreen.popupOversizeInfor.hide();
}

function onCloseOversizeInfor() {
    $.confirm({
        title: 'Xác nhận !',
        content: "Các giá trị thay đổi sẽ bị mất, bạn có muốn đóng?",
        type: 'orange',
        typeAnimated: true,
        draggable: true,
        animationSpeed: 200,
        animation: 'zoom',
        closeAnimation: 'scale',
        buttons: {
            confirm: {
                text: 'Xác nhận',
                btnClass: 'btn-red',
                action: function () {
                    SetDataPopupOversize(overSizeArrayOfPopup);
                    mainScreen.slxIso.option(devExtremeOptions.value, isoDefault);
                    mainScreen.popupOversizeInfor.hide();
                    $('body').css('overflow', 'scroll');
                }
            },
            cancel: {
                text: 'Hủy',
                btnClass: 'btn-dark',
                action: function () {
                    return;
                }
            }
        }
    });
}

function OnChangeIso() {
    let iso = mainScreen.slxIso.option(devExtremeOptions.value);
    $('body').css('overflow', 'hidden');

    setSealRequireFlg(iso);

    if (!iso || iso == '' || iso == 0) {
        $("#linkReeferCont").addClass("isDisabled");
        $("#linkReeferCont").removeClass("pointer");

        $("#linkOverSize").addClass("isDisabled");
        $("#linkOverSize").removeClass("pointer");

        return;
    }

    if (iso && iso > 0) {
        // khi thay đổi ISO sẽ load lại thông tin quá khổ. Cho cả 2 trường hợp Thêm và Sửa
        $.ajax({
            url: "/FullContainerDelivery/GetOgg?iso=" + iso,
            async: false,
        }).done(function (respJson) {
            // load thông tin quá khổ
            let oog = {
                top: respJson.oddOgg.OOG_TOP == null ? 0 : respJson.oddOgg.OOG_TOP,
                right: respJson.oddOgg.OOG_RIGHT == null ? 0 : respJson.oddOgg.OOG_RIGHT,
                left: respJson.oddOgg.OOG_LEFT == null ? 0 : respJson.oddOgg.OOG_LEFT,
                behind: respJson.oddOgg.OOG_BEHIND == null ? 0 : respJson.oddOgg.OOG_BEHIND,
                front: respJson.oddOgg.OOG_FRONT == null ? 0 : respJson.oddOgg.OOG_FRONT,
                length: respJson.oddOgg.OOG_LENGTH == null ? 0 : respJson.oddOgg.OOG_LENGTH,
                height: respJson.oddOgg.OOG_HEIGHT == null ? 0 : respJson.oddOgg.OOG_HEIGHT,
                width: respJson.oddOgg.OOG_WIDTH == null ? 0 : respJson.oddOgg.OOG_WIDTH,
                noOverSize: true,
            }
            overSizeArrayDefault = oog;
            SetDataPopupOversize(oog);
        });
    }

    if (!isContEdit) {
        if (iso.substr(-2, 2) === '30')
            onPopupTemperatureInfor();

        if (iso.substr(-2, 2) === '50' || iso.substr(-2, 2) === '60') {
            onPopupOversizeInfor();
        }
    }
    isContEdit = false;
    if (iso.substr(-2, 2) === '30') {
        $("#linkReeferCont").removeClass("isDisabled");
        $("#linkReeferCont").addClass("pointer");
    } else {
        $("#linkReeferCont").addClass("isDisabled");
        $("#linkReeferCont").removeClass("pointer");
    }

    if (iso.substr(-2, 2) === '50' || iso.substr(-2, 2) === '60') {
        $("#linkOverSize").removeClass("isDisabled");
        $("#linkOverSize").addClass("pointer");
    } else {
        $("#linkOverSize").addClass("isDisabled");
        $("#linkOverSize").removeClass("pointer");
    }
}

function OnChangeOversize(val) {
    var iso = mainScreen.slxIso.option(opt.value);

    if (iso.substr(-2, 2) === '50') {
        if (val == 1) {
            mainScreen.numOversize_Top.option('disabled', false);
            mainScreen.numOversize_Height.option('disabled', true);
        } else {
            mainScreen.numOversize_Top.option('disabled', true);
            mainScreen.numOversize_Height.option('disabled', false);
        }
    }

    if (iso.substr(-2, 2) === '60') {
        if (val == 1) {
            mainScreen.numOversize_Top.option('disabled', false);
            mainScreen.numOversize_Left.option('disabled', false);
            mainScreen.numOversize_Right.option('disabled', false);
            mainScreen.numOversize_Front.option('disabled', false);
            mainScreen.numOversize_Behind.option('disabled', false);
            mainScreen.numOversize_Length.option('disabled', true);
            mainScreen.numOversize_Height.option('disabled', true);
            mainScreen.numOversize_Width.option('disabled', true);
        } else {
            mainScreen.numOversize_Top.option('disabled', true);
            mainScreen.numOversize_Left.option('disabled', true);
            mainScreen.numOversize_Right.option('disabled', true);
            mainScreen.numOversize_Front.option('disabled', true);
            mainScreen.numOversize_Behind.option('disabled', true);
            mainScreen.numOversize_Length.option('disabled', false);
            mainScreen.numOversize_Height.option('disabled', false);
            mainScreen.numOversize_Width.option('disabled', false);
        }
    }
}

function OnNoOversizeChange(opt) {
    if (!opt.value) {
        if ($('#txtOversize_Opt1').is(':checked')) {
            OnChangeOversize(1);
        }
        if ($('#txtOversize_Opt2').is(':checked')) {
            OnChangeOversize(2);
        }
        OnChangeSize();
        $('#txtOversize_Opt1').prop("disabled", false);
        $('#txtOversize_Opt2').prop("disabled", false);
    } else {
        $('#txtOversize_Opt1').prop("disabled", true);
        $('#txtOversize_Opt2').prop("disabled", true);

        mainScreen.numOversize_Top.option('disabled', true);
        mainScreen.numOversize_Height.option('disabled', true);
    }
}

function OnChangeTop() {
    let top = Number(mainScreen.numOversize_Top.option(opt.value));
    let height = Number(mainScreen.numOversize_Height.option(opt.value));
    let heightFixed = Number.parseFloat((Number(top) + overSizeArrayDefault.height).toFixed(3));

    if (height === heightFixed) {
        return;
    }

    mainScreen.numOversize_Height.option(opt.value, heightFixed);

    OnChangeSize();
}

function OnChangeRightOrLeft() {
    let right = Number(mainScreen.numOversize_Right.option(opt.value));
    let left = Number(mainScreen.numOversize_Left.option(opt.value));
    let width = mainScreen.numOversize_Width.option(opt.value);

    let widthFixed = Number.parseFloat(Number(left + right + overSizeArrayDefault.width).toFixed(1));

    if (Number(width) === widthFixed) {
        return;
    }
    mainScreen.numOversize_Width.option(opt.value, widthFixed);

    OnChangeSize();
}

function OnChangeFrontOrBehind() {
    let behind = mainScreen.numOversize_Behind.option(opt.value);
    let front = mainScreen.numOversize_Front.option(opt.value);
    let length = mainScreen.numOversize_Length.option(opt.value);

    if (length === front + behind + overSizeArrayDefault.length) {
        return;
    }

    let lengthFixed = Number.parseFloat(Number(front + behind + overSizeArrayDefault.length).toFixed(1));
    // thực hiện tính
    mainScreen.numOversize_Length.option(opt.value, lengthFixed);

    OnChangeSize();
}

function OnChangeHeight() {
    let height = mainScreen.numOversize_Height.option(opt.value);
    let top = mainScreen.numOversize_Top.option(opt.value);

    //Kiểm tra giới hạn
    if (height < overSizeArrayDefault.height) {
        mainScreen.numOversize_Height.option(opt.value, overSizeArrayDefault.height);
        return;
    }

    // thực hiện tính
    var heightFixed = Number((height - overSizeArrayDefault.height).toFixed());

    if (heightFixed === top) {
        return;
    }

    mainScreen.numOversize_Top.option(opt.value, heightFixed);

    OnChangeSize();
}

function OnChangeLength() {
    let length = Number(mainScreen.numOversize_Length.option(opt.value));
    let behind = Number(mainScreen.numOversize_Behind.option(opt.value));
    let front = Number(mainScreen.numOversize_Front.option(opt.value));
    //Kiểm tra giới hạn
    if (length < overSizeArrayDefault.length) {
        mainScreen.numOversize_Length.option(opt.value, overSizeArrayDefault.length);
        return;
    }

    var lengthFixed = Number((length - overSizeArrayDefault.length).toFixed());
    if (lengthFixed === behind + front) {
        return;
    }

    // thực hiện tính

    let handleBehind = lengthFixed / 2;
    let handleFront = lengthFixed / 2;

    mainScreen.numOversize_Behind.option(opt.value, handleBehind <= 0 ? 0 : parseFloat(handleBehind));
    mainScreen.numOversize_Front.option(opt.value, handleFront <= 0 ? 0 : parseFloat(handleFront));

    OnChangeSize();
}

function OnChangeWidth() {
    let width = Number(mainScreen.numOversize_Width.option(opt.value));
    let right = Number(mainScreen.numOversize_Right.option(opt.value));
    let left = Number(mainScreen.numOversize_Left.option(opt.value));
    let widthFixed = Number.parseFloat((width - overSizeArrayDefault.width).toFixed(3));

    //Kiểm tra giới hạn
    if (width < overSizeArrayDefault.width) {
        mainScreen.numOversize_Width.option(opt.value, overSizeArrayDefault.width);
        return;
    }

    if (right + left === widthFixed) {
        return;
    }

    // thực hiện tính
    let handleRight = widthFixed / 2;
    let handleLeft = widthFixed / 2;

    mainScreen.numOversize_Right.option(opt.value, handleRight <= 0 ? 0 : parseFloat(handleRight));
    mainScreen.numOversize_Left.option(opt.value, handleLeft <= 0 ? 0 : parseFloat(handleLeft));

    OnChangeSize();
}

function OnChangeSize() {
    let top = mainScreen.numOversize_Top.option(opt.value);
    let right = mainScreen.numOversize_Right.option(opt.value);
    let left = mainScreen.numOversize_Left.option(opt.value);
    let behind = mainScreen.numOversize_Behind.option(opt.value);
    let front = mainScreen.numOversize_Front.option(opt.value);
    let length = mainScreen.numOversize_Length.option(opt.value);
    let height = mainScreen.numOversize_Height.option(opt.value);
    let width = mainScreen.numOversize_Width.option(opt.value);

    // Check button OverSize
    if ($('#txtOversize_Opt1').is(':checked')) {
        top > overSizeArrayDefault.top ? mainScreen.chkOversize_OH.option(opt.value, true) : mainScreen.chkOversize_OH.option(opt.value, false);
        if (right > overSizeArrayDefault.right || left > overSizeArrayDefault.left)
            mainScreen.chkOversize_OW.option(opt.value, true);
        else mainScreen.chkOversize_OW.option(opt.value, false);

        if (behind > overSizeArrayDefault.behind || front > overSizeArrayDefault.front)
            mainScreen.chkOversize_OL.option(opt.value, true);
        else mainScreen.chkOversize_OL.option(opt.value, false);
    }

    if ($('#txtOversize_Opt2').is(':checked')) {
        let isHeight = height > overSizeArrayDefault.height;
        let isWidth = width > overSizeArrayDefault.width;
        let isLength = length > overSizeArrayDefault.length;
        mainScreen.chkOversize_OH.option(opt.value, isHeight);
        mainScreen.chkOversize_OL.option(opt.value, isLength);
        mainScreen.chkOversize_OW.option(opt.value, isWidth);

        if (isHeight || isWidth || isLength)
            return;
    }
}

function GetOrderDetailOgg() {
    var oddId = mainScreen.txtOrderDetailId.option(opt.value);
    if (oddId == null || oddId == '')
        oddId = 0;
    return $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/GetOrderDetailOgg',
        data: JSON.stringify({ 'OddId': oddId })
    }).done((respJson) => {
        if (respJson) {
            if (respJson.type === messageType.success) {
                var data = respJson.content;
                if (data != null && data.OOG_LENGTH != null) {
                    overSizeArrayOfPopup.top = data.OOG_TOP;
                    overSizeArrayOfPopup.right = data.OOG_RIGHT;
                    overSizeArrayOfPopup.left = data.OOG_LEFT;
                    overSizeArrayOfPopup.behind = data.OOG_BEHIND;
                    overSizeArrayOfPopup.front = data.OOG_FRONT;

                    overSizeArrayOfPopup.length = data.OOG_LENGTH;
                    overSizeArrayOfPopup.height = data.OOG_HEIGHT;
                    overSizeArrayOfPopup.width = data.OOG_WIDTH;

                    // có dữ liệu nên chắc chắn quá khổ -> không check
                    overSizeArrayOfPopup.noOverSize = false;

                    SetDataPopupOversize(overSizeArrayOfPopup);
                }
            } else {
                message.error(respJson.content);
            }
        }
    });
}

function GetOrderDetailService() {
    let storageId = mainScreen.txtStorageId.option(devExtremeOptions.value);
    let temp_services = getDataRegisterServicce(storageId);

    let isChangeService = !isEmpty(temp_services);

    if (!isChangeService) {
        var oddId = mainScreen.txtOrderDetailId.option(opt.value);
        if (oddId == '')
            oddId = 0;
        return $.ajax({
            type: ajaxConfig.POST,
            contentType: ajaxConfig.contentType,
            url: '/FullContainerDelivery/GetOrderDetailServicesByOrderDetailId',
            data: JSON.stringify({ 'OddId': oddId }),
            //async: false
        }).done((respJson) => {
            if (respJson) {
                if (respJson.type === messageType.success) {
                    var data = respJson.content;
                    if (data != null) {
                        mainScreen.rdxService_Inspected.option(opt.value, data.Item1);
                        mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.value, data.Item2);
                        mainScreen.chkSevice_Antiseptic.option(opt.value, data.Item3);
                    }

                    if (mainScreen.chkSevice_Antiseptic.option(opt.value) == true) {
                        mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', false);
                    } else {
                        mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.value, null);
                        mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', true);
                    }
                } else {
                    message.error(respJson.content);
                }
            }
        });
    } else {
        var service = JSON.parse(temp_services);
        mainScreen.rdxService_Inspected.option(opt.value, service[0]);
        mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.value, service[1]);
        mainScreen.chkSevice_Antiseptic.option(opt.value, service[2]);

        if (mainScreen.chkSevice_Antiseptic.option(opt.value) == true) {
            mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', false);
        } else {
            mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.value, null);
            mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', true);
        }
    }
}

function chkAntisepticChange(chk) {
    if (chk.value == true) {
        mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', false);
    } else {
        mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.value, null);
        mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', true);
    }
}

function onResetAntiseptic() {
    mainScreen.rdxService_Inspected.option(opt.value, '');
    mainScreen.chkSevice_Antiseptic.option(opt.value, false);
    mainScreen.slxService_PhoneNumberAntiDepartment.option(opt.value, null);
    mainScreen.slxService_PhoneNumberAntiDepartment.option('disabled', true);
}

function onResetRegisterForm() {
    ResetPopupSpecialStacking();
    onResetAntiseptic();
    mainScreen.chkConfirm.option(devExtremeOptions.value, false);

    if (isSubmit) {
        const orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
        if (orderId)
            loadData(orderId);
    }
    isSubmit = false;
    $('body').css('overflow', 'scroll');
}

function OnChangeIMO() {
    var imo = mainScreen.slxIMO.option(opt.value);
    if (imo != "" && imo != null)
        mainScreen.txtUNNO.option("disabled", false);
    else {
        mainScreen.txtUNNO.option("disabled", true);
        mainScreen.txtUNNO.option(opt.value, '');
    }
}

function setDisableControl(list) {
    if (list && list.length > 0) {
        mainScreen.txtMaSoThue.option(devExtremeOptions.disabled, true);
    } else {
        mainScreen.txtMaSoThue.option(devExtremeOptions.disabled, false);
    }
}

function onContainerNoFocusOut() {
    var itemNo = mainScreen.txtContainerNo.option(devExtremeOptions.value);
    if (isValidCustom && itemEdit && itemNo !== itemEdit.ITEM_NO) {
        message.error(`Container ${itemEdit.ITEM_NO} đã khai báo tờ khai Hải Quan/Số quản lý hàng hóa.</br>
                       Quý khách vui lòng kiểm tra và hủy tờ khai Hải Quan/Số quản lý hàng hóa đã khai cho Container ${itemEdit.ITEM_NO} trước khi chỉnh sửa số Container khác.`);

        return;
    }
    var strConfirm = "Tôi xác nhận container " + itemNo.toUpperCase() + " thuộc khu vực " + siteName + " như trên Booking hãng tàu cấp";
    $('#txtConfirm').html(strConfirm);
}

function onSealNoNoFocusOut() {
    var sealNo = mainScreen.txtSealNo.option(devExtremeOptions.value);
    if (notSealNoInput.test(sealNo)) {
        $.confirm({
            title: 'Thông báo',
            content: "Số seal không bao gồm ký tự có dấu tiếng Việt, và 2 ký tự ( ` và ')",
            type: 'red',
            typeAnimated: true,
            draggable: true,
            animationSpeed: 200,
            animation: 'zoom',
            closeAnimation: 'scale',
            buttons: {
                confirm: {
                    text: 'Ok',
                    btnClass: 'btn-red',
                    action: function () {
                        mainScreen.txtSealNo.focus();
                        var container = document.getElementById('txtSealNo').getElementsByClassName('dx-texteditor-container');
                        var inputTag = container[0].getElementsByClassName('dx-texteditor-input');
                        inputTag[0].select();
                    }
                }
            }
        });
    }
}
function onNew() {
    //TOS-6093
    // xóa storage id cũ
    let storageId = mainScreen.txtStorageId.option(devExtremeOptions.value);
    localStorage.removeItem(storageId);
    // tạo storage mới
    storageId = GenStorageId();
    mainScreen.txtStorageId.option(opt.value, storageId);

    isoDefault = '';
    itemEdit = '';
    isResetService = true;
    var numOrder = mainScreen.nbxSoLuongContainer.option(opt.value);
    var numOdds = 0;
    var orderDetails = mainScreen.gridOrderDetails.option("dataSource").store._array;
    if (orderDetails === undefined)
        orderDetails = mainScreen.gridOrderDetails.option("dataSource").store.data;
    numOdds = orderDetails.length;
    if (isEdit)
        numOdds = numOdds + 1;
    if (numOrder <= numOdds) {
        message.warning("Bạn không thể đăng kí cont vượt quá số lượng đơn hàng");
        return;
    }

    isClosePopup = false;
    mainScreen.txtRegisterID.option(opt.value, 0);
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);

    isEdit = false;
    isValidCustom = false;
    if (!orderId) {
        message.error("Vui lòng chọn lô hàng!");
        return;
    }

    setStateControlByUserRole()
    resetPopupRegister();

    // Clear Popup Thông tin hàng hóa
    if (isUserNewVerComdDecl !== 'Y') {
        resetFormProductInfo();
    }
    else {
        resetFormCommodityInfo();
    }

    resetFormTemperature();
    GetOrderDetailService();
    GetRegisterCode();
    resetOverSize();
    resetSpecialStacking();

    //bật lại cờ tạo mới đăng kí   
    createFlag = true;
}

function onRegisterFormResize() {
    $("#msgLineOper").height($("#msgVessel").height());
    $("#msgVGM").height($("#msgVessel").height() - 10);
}

function onRegisterFormShown() {
    $("#msgLineOper").height($("#msgVessel").height());
    $("#msgVGM").height($("#msgVessel").height() - 10);
}

// Set text type number [0-9]
function onNumericAndDotPress(e) {
    validateKeypress(numericAndDot);
}

function resetOverSize() {
    overSizeArrayDefault.top = 0;
    overSizeArrayDefault.right = 0;
    overSizeArrayDefault.left = 0;
    overSizeArrayDefault.behind = 0;
    overSizeArrayDefault.front = 0;
    overSizeArrayDefault.length = 0;
    overSizeArrayDefault.height = 0;
    overSizeArrayDefault.width = 0;
    overSizeArrayDefault.noOverSize = false;

    SetDataPopupOversize(overSizeArrayDefault);
}

function GenStorageId() {
    return moment().format('YYYYDDmmHHmmssSSS');
}

function resetPopupRegister() {
    mainScreen.txtRegisterID.option(opt.value, '');
    mainScreen.txtOrderDetailId.option(opt.value, '');
    mainScreen.txtOrderDetailNo.option(opt.value, '');
    mainScreen.dtxCreateDate.option(opt.value, new Date());
    mainScreen.txtContainerNo.option(opt.value, '');
    mainScreen.txtSealNo.option(devExtremeOptions.value, '');
    mainScreen.chkVGM.option(opt.value, false);
    mainScreen.numMaxGross.option(opt.value, 0);
    if (ApplyImo == 'Y') {
        mainScreen.txtIMO.option(opt.value, '');
    } else {
        mainScreen.slxIMO.option(opt.value, '');
    }
    mainScreen.txtUNNO.option(opt.value, '');
    mainScreen.txtCertifiedPlace.option(opt.value, '');
    mainScreen.slxIso.option(opt.value, '');
}

function resetPopupService() {
    let storage = mainScreen.txtStorageId.option(devExtremeOptions.value);
    localStorage.removeItem(storage);
    mainScreen.rdxService_Inspected.option('value', null);
    mainScreen.chkSevice_Antiseptic.option(devExtremeOptions.value, false);
}

function resetControlAllPopup() {
    itemEdit = null;

    // Clear Popup phiếu đăng ký
    resetPopupRegister();

    // Clear Popup Thông tin hàng hóa
    if (isUserNewVerComdDecl !== 'Y') {
        resetFormProductInfo();
    }
    else {
        resetFormCommodityInfo();
    }

    // Clear Popup Quá khổ
    resetOverSize();

    // CLear Popop Cont lạnh
    resetFormTemperature();

    // Clear Popup Xếp đỡ
    resetSpecialStacking();

    // Clear Popup Khử trùng
    resetPopupService();
}

function setSealRequireFlg(iso) {

    var sealNoFlg = $('#sealFlg');

    if (sealNoFlg) {

        if (iso == "") { return; }

        httpClient.get('/FullContainerDelivery/CheckRequireSealNo?iso=' + iso)
            .done((res) => {
                if (res == "True") {
                    sealNoFlg.removeAttr("hidden");
                    return;
                }
                sealNoFlg.attr("hidden", "hidden");
            });
    }
}

function checkVesseLock(vesId) {
    if (vesId) {
        $.ajax({
            url: '/FullContainerDelivery/CheckVesselLock?vesId=' + vesId,
            contentType: ajaxConfig.contentType,
            method: ajaxConfig.GET,
            success: function (response) {
                if (response && response.type.toLowerCase() === messageType.success) {
                    vesselLocked = response.locked;
                    getVesselLocked();
                }
            },
        });
    }
}

function getVesselLocked() {
    if (vesselLocked) {
        $.confirm({
            title: 'Thông báo!',
            content: "Tàu/chuyến chưa đến ngày được hạ bãi. Vui lòng kiểm tra thông tin tàu trên trang Eport <a href='/Ships' target='_blank'>tại đây</a> hoặc liên hệ <label>18001188</label> để biết thêm thông tin.",
            type: 'orange',
            typeAnimated: true,
            draggable: true,
            animationSpeed: 300,
            animation: 'zoom',
            closeAnimation: 'scale',
            buttons: {
                confirm: {
                    text: 'Xác nhận',
                    btnClass: 'btn-red'
                },
                cancel: {
                    text: 'Hủy',
                    btnClass: 'btn-dark',
                    action: function () {
                        mainScreen.slxVessel.option(devExtremeOptions.value, '');
                    }
                }
            }
        });
    }

    return vesselLocked;
}

function onPrintEir() {
    const selectedItems = mainScreen.gridOrderDetails.getSelectedRowsData();
    if (selectedItems.length <= 0) {
        message.error("Vui lòng chọn ít nhất một dòng để in Eir.");
        return;
    }
    let lstEir = [];

    for (let i = 0; i < selectedItems.length; i++) {
        if (selectedItems[i] && selectedItems[i].SO_PHIEU && selectedItems[i].SO_PHIEU !== '') {
            lstEir.push(selectedItems[i].SO_PHIEU);
        }
    }

    if (lstEir.length < 1) {
        message.error("Vui lòng chọn phiếu có số phiếu EIR để thực hiện");
        return;
    }

    onPrintMultiEir(lstEir, '');
}

//row index
function dataGrid_rowIndex_cellTemplate(element, info) {
    element.html(info.rowIndex + 1);
}

//Scripts/eport/modules/commodity/commodity-popup.js
//#region TOS-14932: khai bao tt hang hoa
function onShowPopupCommodityInfo() {
    $("#dxScrollViewPopupCommodityInfo").dxScrollView();

    let malo = mainScreen.txtMalo.option(devExtremeOptions.value);
    let orderDetailNo = mainScreen.txtOrderDetailNo.option(opt.value);
    let itemNo = mainScreen.txtContainerNo.option(devExtremeOptions.value);

    initPopup(malo, orderDetailNo, itemNo);
    mainScreen.popupCommodityInfo.show();
}

function callBackOrderDetail() {
    mainScreen.popupCommodityInfo.hide();
}

function onRedirectCommodityInfo() {
    var orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    if (orderId === null || orderId === '') {
        message.error('Vui lòng chọn lô hàng.');
        return;
    }
    window.open(window.location.origin + "/Commodity?orderId=" + orderId + "&operType=FRECV", "_self");
}
function resetFormCommodityInfo() {
    mainScreenPopup.txtCmdInOrderId.option(devExtremeOptions.value, '');
    mainScreenPopup.slCmdInOrderDetailNo.option(devExtremeOptions.value, '');
    mainScreenPopup.slCmdInItemNo.option(devExtremeOptions.value, '');

    dataComdGeneral = [];
    dataComdDetail = [];

    listCommodityDeclaration = [];
    listOldCommodityDeclaration = [];
    dataCommoditiesToCheck = [];
    needToReloadGrid = false;

    mainScreenPopup.gridCommodityGeneral.option({
        dataSource: ''
    });

    mainScreenPopup.gridCommodityDetail.option({
        dataSource: ''
    });
}

function onCloseErrorPopup() {
    mainScreen.listErrorGrid.option({
        dataSource: ''
    });

    mainScreen.popupListErrorPopup.hide();
}

function onSealInputPress(e) {
    validateKeypress(sealNoInput);
}
function onCloseSpecialStacking() {
    mainScreen.popupListErrorPopup.hide();
}
function onShowPopupMSDS() {
    let selectedItems = mainScreen.gridOrderDetails.getSelectedRowsData();
    let orderId = mainScreen.txtMalo.option(devExtremeOptions.value);
    let lstOrderDetailNo = selectedItems.map(item => item.ORDER_DETAIL_NO);
    let lstItemNo = selectedItems.map(item => item.ITEM_NO);
    if (selectedItems && selectedItems.length < 1) {
        message.error("Vui lòng chọn ít nhất 1 container để khai báo MSDS.");
        return;
    }
    if (selectedItems.some(item => item.HAZADOUS_STATUS == "N" || item.OPER_METHOD.trim() != "HBCX")) {
        message.error("Chỉ được phép khai báo MSDS cho container chứa hàng nguy hiểm thuộc phương án HBCX - Hạ bãi chờ xuất tàu.");
        return;
    }
    $.ajax({
        type: ajaxConfig.POST,
        contentType: ajaxConfig.contentType,
        url: '/FullContainerDelivery/PrePopupMSDS',
        data: JSON.stringify({ 'orderId': orderId, 'lstItemNo': lstItemNo, 'lstOrderDetailNo': lstOrderDetailNo })
    }).done((respJson) => {
        if (respJson) {
            if (respJson.type === messageType.success) {
                mainScreen.popupMSDS.show();
                const dataSource = {
                    store: {
                        type: 'array',
                        key: 'ORDER_DETAIL_NO',
                        data: respJson.lstOrderDetail
                    }
                };
                mainScreen.gridContainerDangerous.option({
                    dataSource: dataSource
                });
                
            } else {
                message.error(respJson.content);
            }
        }
    });
}
function onCloseMSDS() {
    mainScreen.popupMSDS.hide();
}